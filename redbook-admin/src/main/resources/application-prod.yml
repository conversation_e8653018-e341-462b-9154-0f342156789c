# 项目相关配置
redbook:
  # 名称
  name: redbook
  # 版本
  version: 3.8.4
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/redbook/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /opt/redbook/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: char

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8111
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.redbook: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
# Spring配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **********************************************************************************************************************************************************************************************
        username: dbMaster
        password: HssWord@123#*(
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: true
        url: *****************************************************************************************************************************************************************
        username: db_rouser
        password: Hss123!@#
      kids:
        enabled: true
        url: **********************************************************************************************************************************************************************************************************
        username: root
        password: RedBookKids@MySQL123!@#
      # 初始连接数
      initialSize: 30
      # 最小连接池数量
      minIdle: 30
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间，单位是毫秒
      maxWait: 10000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 30000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 15000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 30000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: redbook
        login-password: hb@2023.523
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    # 地址
    host: ********
    # 密码
    password: RedBook123!@#
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 8
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 数据源配置



approval:
  # 最终审批人id
  finalApproverId: hb885579
  # 第一审批人id
  firstApproverId: hb077337
  # 客服总监id
  serviceApproverId: hb179443
#pc端相关数据
hss:
  pc:
    host: https://www.hssenglish.com/redbook/remote/

redbookHost:
  syncQuestion: https://x.xiaohongben888.com/api/suit/syncSuit
  reloadSession: https://api.xiaohongben888.com/redbook/user/reloadUserRedisCache
  cleanTabletRedisCache: https://api.xiaohongben888.com/cross-server/cleanTabletRedisCache

#兴业银行回调地址
xingYe:
  callback:
    url: https://m.xiaohongben888.com/prod-api/pay/notify

rongyun:
  appKey: m7ua80gbma0um
  appSecret: RGtZ88DrfSwd9h

sf:
  call:
    url: https://bspgw.sf-express.com/std/service
  client:
    code: HBKJmFrLQmD
  check:
    word: MXsPqhBQJxJMSl8lbVK5CRqOoSEZD3to

rocketmq:
  name-server: *********:9876;*********:9876
  producer:
    group: manger-producer-group

#售后角色id判断
sysRoleId:
  # 签收人
  signUser: 200,204
  # 复判
  judgeUser: 201,205
  # 维修
  repairUser: 202,205
  # 品控
  qcUser: 203,204
# 快递配置
express:
  # 顺丰
  sfHost: https://bspgw.sf-express.com/std/service
  # 客户编码
  sfCustomerCode: HBKJM49SX73
  # 校验码
  sfCheckCode: J7oGiatjvH1mHDXGmRh3Ve9WOSmuHsBM
  # 面单模板 丰密150标准模板100*150
  sfTemplateCode: fm_150_standard_HBKJM49SX73
#少儿英语
kidHost:
  reloadSession: https://kidsapi.xiaohongben888.com/user/reloadUserSession