package com.redbook.web.controller.activity;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.ActivityUser;
import com.redbook.system.service.IActivityUserService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/activity/earth/protect/")
@Api(tags = "地球环保")
public class EarthProtectController extends BaseController {

    @Autowired
    QueryParamUtil queryParamUtil;
    @Autowired
    IActivityUserService activityUserService;

    @GetMapping("list")
    @ApiOperation("查询地球环保列表")
    public TableDataInfo<ActivityUser> list(ActivityUser activityUser) {
        queryParamUtil.setQueryParam(activityUser);
        startPage();
        List<ActivityUser> list = activityUserService.selectActivityUserList(activityUser);
        return getDataTable(list);
    }


    @ApiOperation("导出地球环保数据列表")
    @Log(title = "地球环保", businessType = BusinessType.EXPORT)
    @PostMapping("export")
    public void export(HttpServletResponse response, ActivityUser activityUser) {
        queryParamUtil.setQueryParam(activityUser);
        List<ActivityUser> list = activityUserService.selectActivityUserList(activityUser);
        ExcelUtil<ActivityUser> util = new ExcelUtil<ActivityUser>(ActivityUser.class);
        util.exportExcel(response, list, "地球环保活动数据");
    }
}
