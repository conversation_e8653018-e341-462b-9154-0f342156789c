package com.redbook.web.controller.oldSystem;


import com.redbook.common.core.controller.BaseController;
import com.redbook.common.exception.ServiceException;
import com.redbook.system.domain.StoreGoods;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.oldSystem.IStoreManagerGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021 -11-25 14:22
 * 商品管理
 */
@RestController
@RequestMapping("/storeManagerGoods")
@Api(tags = "商品管理")
public class StoreManagerGoodsAction extends BaseController {

    @Autowired
    IStoreManagerGoodsService storeManagerGoodsService;
    @Autowired
    IAgentService agentService;

    /**
     * 获取商品列表
     */
    @RequestMapping("/selectGoodsList")
    @ApiOperation(value = "获取商品列表", notes = "获取商品列表")
    public Map<String, Object> selectGoodsList(@ApiParam("代理商aid") String aid,@ApiParam("专卖店id")Integer exclusiveShopId) {//,@ApiParam("2:代理商商品  3:专卖店商品") Integer storeType
//        if (!getLoginUser().getUser().getUserType().equals("01")) {
//            throw ServiceException.fail("您没有权限查看！");
//        }
        List<Map<String, Object>> list = storeManagerGoodsService.listGoodsList(aid,exclusiveShopId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("list", list);
        return map;
    }

    @RequestMapping("/deleteGoods")
    @ApiOperation(value = "删除商品", notes = "删除商品")
    public boolean deleteGoods(@ApiParam("代理商aid") String aid,@ApiParam("专卖店id")Integer exclusiveShopId, @ApiParam("商品id") Integer goodsId) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //店长只允许管理专卖店商品
        if(getLoginUser().getUser().getRole().getRoleId().intValue()==106 && exclusiveShopId==null){
            return false;
        }
        boolean flag = storeManagerGoodsService.deleteGoods(aid,exclusiveShopId, goodsId);
        return flag;
    }

    @RequestMapping("/offShelf")
    @ApiOperation(value = "下架商品", notes = "下架商品")
    public boolean offShelf(@ApiParam("代理商aid") String aid,@ApiParam("专卖店id")Integer exclusiveShopId, @ApiParam("商品id") Integer id) {//商品id
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //店长只允许管理专卖店商品
        if(getLoginUser().getUser().getRole().getRoleId().intValue()==106 && exclusiveShopId==null){
            return false;
        }
        boolean flag = storeManagerGoodsService.offShelf(id,exclusiveShopId, aid);
        return flag;
    }

    @RequestMapping("/onShelf")
    @ApiOperation(value = "上架该商品", notes = "上架该商品")
    public boolean onShelf(@ApiParam("商品id") Integer id,@ApiParam("专卖店id")Integer exclusiveShopId, @ApiParam("代理商aid") String aid) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //店长只允许管理专卖店商品
        if(getLoginUser().getUser().getRole().getRoleId().intValue()==106 && exclusiveShopId==null){
            return false;
        }
        boolean flag = storeManagerGoodsService.onShelf(id,exclusiveShopId, aid);
        return flag;
    }

    @RequestMapping("/upload")
    @ApiOperation(value = "添加商品", notes = "添加商品")
    public boolean upload(@ApiParam("名称") String name, @ApiParam("价格") Integer price, @ApiParam("砖石价格") Integer diamonds,
                          @ApiParam("描述") String desc, @ApiParam("库存数") Integer stock, @ApiParam("图片地址") String imageUrl,
                          @ApiParam("专卖店id")Integer exclusiveShopId,
                          @ApiParam("代理商aid") String aid,@ApiParam("标签：1 新品，2 热销，3清仓") Integer label) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //店长只允许管理专卖店商品
        if(getLoginUser().getUser().getRole().getRoleId().intValue()==106 && exclusiveShopId==null){
            return false;
        }
        storeManagerGoodsService.upload(name, price, diamonds, desc, imageUrl, stock, exclusiveShopId, aid, getUserId().intValue(), getUsername(),label);
        return true;
    }


    @RequestMapping("/changeImg")
    @ApiOperation(value = "更换图片", notes = "更换图片")
    public boolean changeImg(@ApiParam("商品id") Integer goodsId, @ApiParam("图片地址") String imgUrl) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        return storeManagerGoodsService.modifyImg(goodsId, imgUrl) > 0;
    }

    @RequestMapping("/modify")
    @ApiOperation("修改商品信息")
    public Boolean modify(@ApiParam("商品id") Integer goodsId, @ApiParam("商品名称") String name, @ApiParam("价格") Integer price, @ApiParam("砖石价格") Integer diamonds, @ApiParam("商品描述") String desc, @ApiParam("库存") Integer stock, @ApiParam("老库存数") Integer oldStock, @ApiParam("图片地址") String imageUrl,@ApiParam("标签：1 新品，2 热销，3清仓") Integer label) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        return storeManagerGoodsService.modify(goodsId, name, price, diamonds, desc, stock, oldStock, getUsername(), imageUrl,label);
    }

    /**
     * 查询登录用户校区下的 商品库存记录集合信息
     *
     * @param goodsName 商品名称
     * @param startDate 开始时间 (入库时间)
     * @param endDate   结束时间 (入库时间)
     * @param pageNum   当前页
     * @param pageSize  每页条数
     * @return Map 库存记录集合信息
     * <AUTHOR>
     */
    @RequestMapping("/findStockRecordListInfo")
    @ApiOperation("查询登录用户校区下的 商品库存记录集合信息")
    public Map findStockRecordListInfo(@ApiParam("代理商aid")String aid, Integer exclusiveShopId,
                                       @ApiParam("商品名称") String goodsName, @ApiParam("开始日期 入库时间") String startDate,
                                       @ApiParam("结束日期 入库时间") String endDate, @ApiParam("当前页") Integer pageNum, @ApiParam("每页条数") Integer pageSize) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> resultMap = storeManagerGoodsService.findStockRecordListInfo(aid,exclusiveShopId,goodsName, startDate, endDate, pageNum, pageSize);
            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", 500);
            result.put("msg", "exception");
            result.put("data", null);
        }
        return result;
    }

    /**
     * 添加商品库存
     *
     * @param goodsId        商品ID
     * @param insertStockNum 添加库存数
     * @return Boolean 成功/失败
     * <AUTHOR>
     */
    @RequestMapping("/insertStock")
    @ApiOperation("添加商品库存")
    public Boolean insertStock(@ApiParam("商品id") Integer goodsId, @ApiParam("添加库存数") Integer insertStockNum) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //获取登录用户信息
        return storeManagerGoodsService.insertStock(goodsId, insertStockNum, getUsername());
    }

    @RequestMapping("/setSort")
    @ApiOperation("设置商品排序")
    public Boolean setSort(@ApiParam("商品id") Integer goodsId, @ApiParam("排序") Integer sort) {
        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
        //获取登录用户信息
        return storeManagerGoodsService.setSort(goodsId, sort, getUsername());
    }

//    /**
//     * 查询登录用户校区下的 商品订单记录集合信息
//     *
//     * @param orderId   订单编号
//     * @param goodsName 商品名称
//     * @param startDate 开始时间 (入库时间)
//     * @param endDate   结束时间 (入库时间)
//     * @param pageNum   当前页
//     * @param pageSize  每页条数
//     * @return Map 订单记录集合信息
//     * <AUTHOR>
//     */
//    @RequestMapping("/findOrderRecordListInfo")
//    @ApiOperation("查询登录用户校区下的 商品订单记录集合信息")
//    public Map findOrderRecordListInfo(@ApiParam("订单编号") Integer orderId, @ApiParam("商品名称") String goodsName, @ApiParam("开始日期 入库时间") String startDate, @ApiParam("结束日期 入库时间") String endDate, @ApiParam("当前页") Integer pageNum, @ApiParam("每页条数") Integer pageSize, @ApiParam("代理商aid") String aid) {
//        if (!getLoginUser().getUser().getUserType().equals("01")) throw ServiceException.fail("您没有权限查看！");
//        Map<String, Object> result = new HashMap<>();
//        try {
//            //获取登录用户信息
//            Map<String, Object> resultMap = storeManagerGoodsService.findOrderRecordListInfo(orderId, goodsName, startDate, endDate, aid, pageNum, pageSize);
//            result.put("code", 200);
//            result.put("msg", "success");
//            result.put("data", resultMap);
//        } catch (Exception e) {
//            e.printStackTrace();
//            result.put("code", 500);
//            result.put("msg", "exception");
//            result.put("data", null);
//        }
//        return result;
//    }

}
