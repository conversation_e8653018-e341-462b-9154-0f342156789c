package com.redbook.web.controller.student;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.MemberRenewChargingDto;
import com.redbook.system.domain.model.UserBean;
import com.redbook.system.mapper.IMemberRenewDao;
import com.redbook.system.service.*;
import com.redbook.system.util.DateUtil;
import com.redbook.system.util.RemoteUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@Api(tags = "学生会员续费")
@RestController
@RequestMapping("/agent/student")
public class StudentMemberController extends BaseController {

    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    IRedBookPcUserService redBookPcUserService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IMemberRenewDao renewDao;
    @Autowired
    private RedBookRenewService redBookRenewService;
    @Autowired
    IAgentService agentService;
    @Autowired
    RedisCache redisCache;
    @Autowired
    RemoteUtil remoteUtil;
    @Autowired
    IElectronicRenewCardService electronicRenewCardService;
    @Autowired
    IExclusiveShopService exclusiveShopService;
    @Autowired
    private IRedbookAgentRebateService rebateService;
    @Autowired
    private IRedbookMemberRebatesService redbookMemberRebatesService;


    @RequestMapping("/exclusiveShopRenewMember")
    @ApiOperation("（专卖店）续费")
    @ResponseBody
    @Transactional
    @Log(title = "学生续费", businessType = BusinessType.INSERT)
    public AjaxResult exclusiveShopRenewMember(@ApiParam("专卖店ID") @NotNull Integer exclusiveShopId,
                                               @ApiParam("续费用户id") String userId,
                                               @ApiParam("续费阶段") int renewStage,
                                               @ApiParam("续费时长") int renewTimeLen,
//                                  @ApiParam("赠送时长") int moreTimeLen,
                                               @ApiParam("支付密码") String payPasswd,
                                               @ApiParam("活动id") Integer activityContentId,
                                               @ApiParam("是否参加单词训练营") Boolean joinActivity,
                                               @ApiParam("是否送龙币") Boolean sendCoin) {
        //处理重复提交
        String key = "renewMember_" + userId;
        if (!redisCache.addlock(key, "1", 60)) {
            return AjaxResult.error("请勿重复提交！");
        }
        //临时需求，23NH校验
        if (userId.contains("23NH")) {
            return AjaxResult.error("此[23NH]前缀账号不可续费！");
        }
        try {
            //20240415赠送时长取消
            int moreTimeLen = 0;
            if (getLoginUser().getUser().getRole().getRoleId().intValue() != 106) {
                return AjaxResult.error("您没有权限，只有店长可以操作！");
            }
            Map<String, Object> userMap = redBookPcUserService.getStudentBasicInfo(userId);
            if (userMap != null && userMap.get("access_date") == null) {
                return AjaxResult.error("课程号未激活，无法进行续费！");
            }
            UserInfo redBookUserInfo = userInfoService.selectUserInfoByUserId(userId);
            if (redBookUserInfo == null) {
                return AjaxResult.error("用户不存在！");
            }
            if (exclusiveShopId == null || redBookUserInfo.getExclusiveShopId() == null || redBookUserInfo.getExclusiveShopId().intValue() != exclusiveShopId.intValue()) {
                return AjaxResult.error("只能使用会员所在专卖店的账户支付！");
            }
            Agent agentBean = agentService.getByAid(redBookUserInfo.getAid());
            if (agentBean == null) {
                return AjaxResult.error("无法续费，用户未分配代理商！");
            }
            if (renewStage == 0) { //0元转
                return AjaxResult.error("专卖店不支持0元转操作，请联系签约人操作！");
            }
            MemberRenewChargingDto memberRenewChargingDto = redBookRenewService.charging(null, exclusiveShopId, redBookUserInfo.getUsername() + "( " + redBookUserInfo.getUserId() + ")", payPasswd, renewStage, renewTimeLen);
            if (memberRenewChargingDto.getErrorCode() != null) {
                return AjaxResult.error(memberRenewChargingDto.getErrorCode(), memberRenewChargingDto.getErrorMsg());
            }
            return redBookRenewService.renewRedBook(memberRenewChargingDto.getPayAgentId(), memberRenewChargingDto.getPayExclusiveShopId(), memberRenewChargingDto.getIndentNumber(), userId, renewStage, renewTimeLen,
                    moreTimeLen, memberRenewChargingDto.getAgentPay(), memberRenewChargingDto.getExclusiveShopPay(), null, activityContentId, joinActivity, sendCoin);
        } finally {
            redisCache.releaseLock(key, "1");
        }
    }


    //续费
    @RequestMapping("/renewMember")
    @ApiOperation("（代理商）续费")
    @ResponseBody
    @Transactional
    @Log(title = "学生续费", businessType = BusinessType.INSERT)
    public AjaxResult renewMember(@ApiParam("付款代理商id") @NotNull long agentId,
                                  @ApiParam("续费用户id") String userId,
                                  @ApiParam("续费阶段") int renewStage,
                                  @ApiParam("续费时长") int renewTimeLen,
//                                  @ApiParam("赠送时长") int moreTimeLen,
                                  @ApiParam("支付密码") String payPasswd,
                                  @ApiParam("活动id") Integer activityContentId,
                                  @ApiParam("是否参加单词训练营") Boolean joinActivity,
                                  @ApiParam("是否送龙币") Boolean sendCoin) {
        //处理重复提交
        String key = "renewMember_" + userId;
        if (!redisCache.addlock(key, "1", 60)) {
            return AjaxResult.error("请勿重复提交！");
        }
        //临时需求，23NH校验
        if (userId.contains("23NH")) {
            return AjaxResult.error("此[23NH]前缀账号不可续费！");
        }
        try {
            //20240415赠送时长取消
            int moreTimeLen = 0;
            if (!SecurityUtils.getLoginUser().getUser().getUserType().equals("01")) {
                return AjaxResult.error("您没有权限！");
            }
            Map<String, Object> userMap = redBookPcUserService.getStudentBasicInfo(userId);
            if (userMap != null && userMap.get("access_date") == null) {
                return AjaxResult.error("课程号未激活，无法进行续费！");
            }
            UserInfo redBookUserInfo = userInfoService.selectUserInfoByUserId(userId);
            if (redBookUserInfo == null) {
                return AjaxResult.error("用户不存在！");
            }
            Agent agentBean = agentService.getByAid(redBookUserInfo.getAid());
            if (agentBean == null) {
                return AjaxResult.error("无法续费，用户未分配代理商！");
            }
            if (renewStage == 0) { //老学员直接转会员,0元转
                String moreTime = "";
                Date lastAccessDate = DateUtil.stringtoDate(String.valueOf(userMap.get("last_access_date")), "yyyy-MM-dd");
                Date nowDate = new Date();
                Calendar afterDate = Calendar.getInstance();
                if (lastAccessDate.before(nowDate)) {//账号已过期
                    return AjaxResult.error("账号已到期，不支持0元转操作！");
                } else {
                    afterDate.setTime(lastAccessDate);
                }
                if (moreTimeLen == 1) {
                    afterDate.add(Calendar.DAY_OF_YEAR, 7);
                    moreTime = "7 DAY";
                } else if (moreTimeLen == 2) {
                    afterDate.add(Calendar.DAY_OF_YEAR, 15);
                    moreTime = "15 DAY";
                } else if (moreTimeLen == 3) {
                    afterDate.add(Calendar.MONTH, 1);
                    moreTime = "1 MONTH";
                }

                if (redBookUserInfo != null) {
                    Date expirationDate = redBookUserInfo.getExpirationDate();
                    if (afterDate.before(expirationDate)) {
                        afterDate.setTime(expirationDate);
                    }
                }

                String commedityCode = userMap.get("commedity_code").toString();

                //PC的VIP会员也能0元转
                if (userMap.get("purchase").toString().equals("vip,") && commedityCode.equals("901")) {
                    redBookRenewService.zeroRenewPCVIP(userId);
                    return AjaxResult.success("转会员成功");
                }
                if ((commedityCode.startsWith("2") && !commedityCode.equals("202")) || commedityCode.startsWith("9")) {
                    return AjaxResult.error("该学员不允许转会员！");
                }
                if (commedityCode.equals("101") || commedityCode.equals("102") || commedityCode.equals("105") || commedityCode.equals("106") || commedityCode.equals("114")) {
                    List<Map> renewCardList = redBookPcUserService.getRenewCardByUserId(userId);
                    if (renewCardList == null || renewCardList.size() == 0) {
                        return AjaxResult.error("该学员不允许直接转会员！");
                    }
                }
                //调用dubbo服务
                boolean dubboFlag = redBookRenewService.zeroRenew(SecurityUtils.getLoginUser().getUserId(), agentId, userId, moreTime);
                if (dubboFlag) {
                    return AjaxResult.success("转会员成功");
                } else {
                    return AjaxResult.success("转会员失败，服务异常");
                }
            }
            MemberRenewChargingDto memberRenewChargingDto = redBookRenewService.charging(agentId, null, redBookUserInfo.getUsername() + "( " + redBookUserInfo.getUserId() + ")", payPasswd, renewStage, renewTimeLen);
            if (memberRenewChargingDto.getErrorCode() != null) {
                return AjaxResult.error(memberRenewChargingDto.getErrorCode(), memberRenewChargingDto.getErrorMsg());
            }
            return redBookRenewService.renewRedBook(memberRenewChargingDto.getPayAgentId(), null, memberRenewChargingDto.getIndentNumber(), userId, renewStage, renewTimeLen,
                    moreTimeLen, memberRenewChargingDto.getAgentPay(), null, null, activityContentId, joinActivity, sendCoin);
        } finally {
            redisCache.releaseLock(key, "1");
        }
    }

    //退费
    @PostMapping("/refund")
    @ApiOperation("退费")
    @ResponseBody
    @Log(title = "学生退费", businessType = BusinessType.INSERT)
    public AjaxResult<Boolean> refund(Integer id) {
        return AjaxResult.success(redBookRenewService.refundRedBook(id));
    }


    @GetMapping("/getWantRenewStudent")
    @ApiOperation("续约前查询是否可以0元转以及是否新购")
    @ResponseBody
    public AjaxResult getWantRenewStudent(String userId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        UserInfo redBookUser = userInfoService.selectUserInfoByUserId(userId);
        if (redBookUser == null) {
            hashMap.put("code", "3");
            hashMap.put("msg", "小红本不存在该会员！");
            return success(hashMap);
        }
        hashMap.put("zeroFlag", 0);
        Integer memberType = redBookUser.getMemberType();
        hashMap.put("memberType", memberType);
        if (memberType == -1) {
            //查询是否是pc旧账号
            UserBean pcUserBean = remoteUtil.getUserByUserId(userId);

            if (pcUserBean == null) {
                hashMap.put("zeroFlag", 0);
            } else {
                //判断是否到期
                Date lastAccessDate = pcUserBean.getLastAccessDate();
                if (redBookUser.getExpirationDate().getTime() >= pcUserBean.getLastAccessDate().getTime()) {//如果小红本的体验时间大于PC端到期时间，就按照小红本体验到期日期增加时长
                    if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
                        hashMap.put("lastAccessDate", DateUtil.dateToString(redBookUser.getExpirationDate()));
                    } else {
                        hashMap.put("lastAccessDate", DateUtil.dateToString(lastAccessDate));
                    }
                } else {
                    hashMap.put("lastAccessDate", DateUtil.dateToString(lastAccessDate));
                }
                if (lastAccessDate != null && !lastAccessDate.equals("")) {
                    Date nowDate = new Date();
                    if (lastAccessDate.before(nowDate)) {
                        hashMap.put("zeroFlag", 0);
                    } else {
                        String commedityCode = pcUserBean.getCommedityCode();
                        if (commedityCode.equals("101") || commedityCode.equals("102") || commedityCode.equals("105") || commedityCode.equals("106") || commedityCode.equals("114")
                                || commedityCode.equals("401") || commedityCode.equals("402") || commedityCode.equals("403") || commedityCode.equals("413") || commedityCode.equals("415")
                                || commedityCode.equals("404") || commedityCode.equals("405") || commedityCode.equals("406") || commedityCode.equals("414")) {//分册课程和小升初课程
                            List<Map> renewCardList = redBookPcUserService.getRenewCardByUserId(userId);
                            if (renewCardList != null && renewCardList.size() > 0) {
                                hashMap.put("zeroFlag", 1);
                            } else {
                                hashMap.put("zeroFlag", 0);
                            }
                        } else {
                            hashMap.put("zeroFlag", 1);
                        }
                    }
                } else {
                    hashMap.put("zeroFlag", 0);
                }
            }
        }
        boolean isFirstRenew = renewDao.getMemberPayRenewNum(userId) == 0;
        if (isFirstRenew) {
            hashMap.put("isRenew", 1);
            //2024年 4 月 15日后不赠送时长
            if (LocalDate.now().isAfter(LocalDate.of(2024, 4, 14))) {
                hashMap.put("isRenew", 0);
            }
        } else {
            hashMap.put("isRenew", 0);
        }
        hashMap.put("code", "200");
        return success(hashMap);
    }


    @RequestMapping("/renewCard")
    @ApiOperation("使用电子卡续费")
    @ResponseBody
    @Log(title = "使用电子卡续费", businessType = BusinessType.INSERT)
    public AjaxResult renewMemberCard(
            @ApiParam("续费用户id") String userId,
            @NotNull @ApiParam("电子卡号") String electronicRenewCardNumber,
            @ApiParam("续费阶段") int renewStage,
            @ApiParam("续费时长") int renewTimeLen,
//            @ApiParam("赠送时长") int moreTimeLen,
            @NotNull @ApiParam("支付密码") String payPasswd,
            @ApiParam("活动id") Integer activityContentId,
            @ApiParam("是否参加单词训练营") Boolean joinActivity,
            @ApiParam("是否送龙币") Boolean sendCoin) {
        if (!getLoginUser().getUser().getUserType().equals("01")) {
            return AjaxResult.error("您没有权限！");
        }
        //临时需求，23NH校验
        if (userId.contains("23NH")) {
            return AjaxResult.error("此[23NH]前缀账号不可续费！");
        }
        Boolean status = electronicRenewCardService.status(electronicRenewCardNumber, renewTimeLen, userId);
        if (status) {
            //20240415赠送时长取消
            int moreTimeLen = 0;
            Map<String, Object> userMap = redBookPcUserService.getStudentBasicInfo(userId);
            if (userMap != null && userMap.get("access_date") == null) {
                return AjaxResult.error("课程号未激活，无法进行续费！");
            }
            UserInfo redBookUser = userInfoService.selectUserInfoByUserId(userId);
            if (redBookUser == null) {
                return AjaxResult.error("用户不存在！");
            }
            Agent agentBean = agentService.getByAid(redBookUser.getAid());
            if (agentBean == null) {
                return AjaxResult.error("无法续费，用户未分配代理商！");
            }
            ElectronicRenewCard renewCard = electronicRenewCardService.selectElectronicRenewCardByCardNumber(electronicRenewCardNumber);
            Long agentId = renewCard.getAgentId();
            Integer exclusiveShopId = null;
            //店长，验证支付密码
            if (getLoginUser().getUser().getRole().getRoleId().intValue() == 106) {
                if (redBookUser.getExclusiveShopId() == null) {
                    return AjaxResult.error("无法续费，用户未分配专卖店！");
                }
                if (renewCard.getExclusiveShopId() == null) {
                    return AjaxResult.error("只能使用专卖店下的续费卡！");
                }
                ExclusiveShop exclusiveShop = exclusiveShopService.selectExclusiveShopById(renewCard.getExclusiveShopId());
                if (exclusiveShop == null) {
                    return AjaxResult.error("无法续费，专卖店不存在！");
                }
                if (exclusiveShop.getManagerId().longValue() != getLoginUser().getUserId().longValue()) {
                    return AjaxResult.error("无法续费，只能使用您管理的专卖店进行续费！");
                }
                if (exclusiveShop.getManagerId().longValue() != getLoginUser().getUserId().longValue()) {
                    return AjaxResult.error("无法续费，只能使用您管理的专卖店进行续费！");
                }
                if (!exclusiveShopService.checkPayPassword(renewCard.getExclusiveShopId(), payPasswd)) {
                    return AjaxResult.error("支付密码错误！");
                }
                exclusiveShopId = renewCard.getExclusiveShopId();
            } else {
                if (!agentAccountService.checkPayPassword(agentId, payPasswd)) {
                    return AjaxResult.error("支付密码错误！");
                }
            }
            return this.redBookRenewService.renewRedBook(agentId, exclusiveShopId, null, userId,
                    renewStage, renewTimeLen, moreTimeLen, BigDecimal.ZERO, BigDecimal.ZERO,
                    electronicRenewCardNumber, activityContentId, joinActivity, sendCoin);
        }
        return success();
    }

    @GetMapping("/getPrice")
    @ApiOperation("获取续费价格")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "renewTimeLen", value = "续费时长", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "agentId", value = "代理商id", required = true, dataType = "Long", paramType = "query")
    }
    )
    public AjaxResult<BigDecimal> getPrice(Integer renewTimeLen, Long agentId) {
        return AjaxResult.success(rebateService.getAgentRebatePrice(agentId, renewTimeLen));
    }

    @GetMapping("/getAgentRebate")
    @ApiOperation("获取代理商动态价格优惠")
    public AjaxResult<RedbookAgentRebate> getAgentRebate(Long agentId) {
        RedbookAgentRebate rebate = rebateService.getAgentRebateByAgentId(agentId);
        if (rebate == null) {
            return AjaxResult.error("代理商动态价格优惠不存在！");
        }
        return AjaxResult.success(rebate);
    }

    @GetMapping("/rebateList")
    @ApiOperation("动态价格优惠档位列表")
    @ResponseBody
    public HashMap rebateList(Long agentId) {
        HashMap<String, Object> result = new HashMap<>();
        List<RedbookMemberRebates> rebates = redbookMemberRebatesService.selectRedbookMemberRebatesList(null);
        result.put("rebates",rebates);
        if (agentId!=null){
            result.put("currentRebates",rebateService.getAgentRebateByAgentId(agentId));
        }
        return result;
    }
}
