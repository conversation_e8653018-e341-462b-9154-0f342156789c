package com.redbook.web.controller.kids;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.kids.KidUserInfo;
import com.redbook.system.domain.dto.MemberRenewChargingDto;
import com.redbook.system.domain.dto.UserClassChangeDto;
import com.redbook.system.domain.vo.ClassInfo;
import com.redbook.system.domain.vo.TableDataSummaryInfoVo;
import com.redbook.system.service.*;
import com.redbook.system.service.kids.IKidUserInfoService;
import com.redbook.system.util.QueryParamUtilKids;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/kids/user")
@Api(tags = "棒棒糖用户管理")
public class KidsUserController extends BaseController {
    @Autowired
    QueryParamUtilKids queryParamUtil;
    @Autowired
    IKidUserInfoService userInfoService;

    @Autowired
    private RedBookRenewService redBookRenewService;
    @Autowired
    IAgentService agentService;
    @Autowired
    RedisCache redisCache;


    @GetMapping("/list")
    @ApiOperation("会员管理列表")
    public TableDataInfo<KidUserInfo> list(KidUserInfo userInfo) {
        queryParamUtil.setQueryParamGetAidList(userInfo);
        this.startPageNoCount();
        return userInfoService.selectUserInfoList(userInfo);
    }

    @GetMapping("/list/info")
    @ApiOperation("会员管理列表汇总信息")
    public TableDataSummaryInfoVo info(KidUserInfo userInfo) {
        queryParamUtil.setQueryParamGetAidList(userInfo);
        this.closePagination();
        return userInfoService.countUserInfoList(userInfo);
    }


    /**
     * 修改学生信息
     *
     * @param user
     * @return
     */
    @PutMapping("/updateStudentInfo")
    @ApiOperation(value = "修改学生信息")
    @ResponseBody
    public AjaxResult updateStudentInfo(KidUserInfo user) {
        String password = user.getPassword();
        if (password != null && !"".equals(password.trim())) {
            password = Md5Utils.hash(password);
            user.setPassword(password);
        }
        if (null != user.getUserName()) {
            user.setUserName(user.getUserName().replaceAll(" ", ""));
            if (user.getUserName().length() > 10) {
                user.setUserName(user.getUserName().substring(0, 10));
            }
        }
        return AjaxResult.success(userInfoService.updateStudentInfo(user));
    }


    @GetMapping("/updateStudentPassword")
    @ApiOperation(value = "重置密码")
    @ResponseBody
    public AjaxResult resetPassword(String userId) {
        return AjaxResult.success("操作成功", userInfoService.resetPassword(userId));
    }


    @ApiOperation("获取班级列表")
    @GetMapping("/getClassList")
    @ResponseBody
    public AjaxResult<List<ClassInfo>> getClassList(String userId) {
        return AjaxResult.success(userInfoService.getClassList(userId));
    }

    @ApiOperation("修改学生班级")
    @PostMapping("/updateStudentClass")
    @Log(title = "修改学生班级", businessType = BusinessType.UPDATE)
    @ResponseBody
    public AjaxResult updateStudentClass(@RequestBody UserClassChangeDto dto) {
        return AjaxResult.success(userInfoService.updateStudentClass(dto));
    }


    @RequestMapping("/exclusiveShopRenewMember")
    @ApiOperation("（专卖店）续费")
    @ResponseBody
    @Log(title = "学生续费", businessType = BusinessType.INSERT)
    public AjaxResult exclusiveShopRenewMember(@ApiParam("专卖店ID") @NotNull Integer exclusiveShopId,
                                               @ApiParam("续费用户id") String userId,
                                               @ApiParam("续费时长") int renewTimeLen,
                                               @ApiParam("支付密码") String payPasswd) {
        Integer renewStage=99;
        //处理重复提交
        String key = "renewMember_" + userId;
        if (!redisCache.addlock(key, "1", 60)) {
            return AjaxResult.error("请勿重复提交！");
        }
        //临时需求，23NH校验
        if (userId.contains("23NH")) {
            return AjaxResult.error("此[23NH]前缀账号不可续费！");
        }
        try {
            //20240415赠送时长取消
            int moreTimeLen = 0;
            if (getLoginUser().getUser().getRole().getRoleId().intValue() != 106) {
                return AjaxResult.error("您没有权限，只有店长可以操作！");
            }
            KidUserInfo redBookUserInfo = userInfoService.selectUserInfoByUserId(userId);
            if (redBookUserInfo == null) {
                return AjaxResult.error("用户不存在！");
            }
            if (exclusiveShopId == null || redBookUserInfo.getExclusiveShopId() == null || redBookUserInfo.getExclusiveShopId().intValue() != exclusiveShopId.intValue()) {
                return AjaxResult.error("只能使用会员所在专卖店的账户支付！");
            }
            Agent agentBean = agentService.getByAid(redBookUserInfo.getAid());
            if (agentBean == null) {
                return AjaxResult.error("无法续费，用户未分配代理商！");
            }
            MemberRenewChargingDto memberRenewChargingDto = redBookRenewService.charging(null, exclusiveShopId, redBookUserInfo.getUserName() + "( " + redBookUserInfo.getUserId() + ")", payPasswd, renewStage, renewTimeLen);
            if (memberRenewChargingDto.getErrorCode() != null) {
                return AjaxResult.error(memberRenewChargingDto.getErrorCode(), memberRenewChargingDto.getErrorMsg());
            }
            return redBookRenewService.renewKids(memberRenewChargingDto.getPayAgentId(), memberRenewChargingDto.getPayExclusiveShopId(),  memberRenewChargingDto.getIndentNumber(), userId, renewStage, renewTimeLen,memberRenewChargingDto.getAgentPay(), memberRenewChargingDto.getExclusiveShopPay());

        } finally {
            redisCache.releaseLock(key, "1");
        }
    }


    //续费
    @RequestMapping("/renewMember")
    @ApiOperation("（代理商）续费")
    @ResponseBody
    @Log(title = "学生续费", businessType = BusinessType.INSERT)
    public AjaxResult renewMember(@ApiParam("付款代理商id") @NotNull long agentId,
                                  @ApiParam("续费用户id") String userId,
                                  @ApiParam("续费时长") int renewTimeLen,
                                  @ApiParam("支付密码") String payPasswd) {
        Integer renewStage=99;
        //处理重复提交
        String key = "renewMember_" + userId;
        if (!redisCache.addlock(key, "1", 60)) {
            return AjaxResult.error("请勿重复提交！");
        }
        try {
            //20240415赠送时长取消
            int moreTimeLen = 0;
            if (!SecurityUtils.getLoginUser().getUser().getUserType().equals("01")) {
                return AjaxResult.error("您没有权限！");
            }
            KidUserInfo redBookUserInfo = userInfoService.selectUserInfoByUserId(userId);
            if (redBookUserInfo == null) {
                return AjaxResult.error("用户不存在！");
            }
            Agent agentBean = agentService.getByAid(redBookUserInfo.getAid());
            if (agentBean == null) {
                return AjaxResult.error("无法续费，用户未分配代理商！");
            }
            MemberRenewChargingDto memberRenewChargingDto = redBookRenewService.charging(agentId, null, redBookUserInfo.getUserName() + "( " + redBookUserInfo.getUserId() + ")", payPasswd, renewStage, renewTimeLen);
            if (memberRenewChargingDto.getErrorCode() != null) {
                return AjaxResult.error(memberRenewChargingDto.getErrorCode(), memberRenewChargingDto.getErrorMsg());
            }
            return redBookRenewService.renewKids(memberRenewChargingDto.getPayAgentId(),null,  memberRenewChargingDto.getIndentNumber(), userId, renewStage, renewTimeLen,memberRenewChargingDto.getAgentPay(), memberRenewChargingDto.getExclusiveShopPay());
        } finally {
            redisCache.releaseLock(key, "1");
        }
    }

}
