package com.redbook.web.controller.postSale;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.domain.PostSaleExpressCompany;
import com.redbook.system.service.postSale.IPostSaleExpressCompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 售后快递公司Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequestMapping("/postSale/admin/expressCompany")
@Api(tags = "售后快递公司")
public class PostSaleExpressCompanyController extends BaseController {
    @Autowired
    private IPostSaleExpressCompanyService postSaleExpressCompanyService;

    /**
     * 查询售后快递公司列表
     */
    @GetMapping("/list")
    @ApiOperation("查询售后快递公司列表")
    public TableDataInfo<PostSaleExpressCompany> list(PostSaleExpressCompany postSaleExpressCompany) {
        startPage();
        List<PostSaleExpressCompany> list = postSaleExpressCompanyService.selectPostSaleExpressCompanyList(postSaleExpressCompany);
        return getDataTable(list);
    }

    /**
     * 新增售后快递公司
     */
    @ApiOperation("新增售后快递公司")
    @Log(title = "售后快递公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleExpressCompany postSaleExpressCompany) {
        return toAjax(postSaleExpressCompanyService.insertPostSaleExpressCompany(postSaleExpressCompany));
    }

    /**
     * 修改售后快递公司
     */
    @ApiOperation("修改售后快递公司")
    @Log(title = "售后快递公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleExpressCompany postSaleExpressCompany) {
        return toAjax(postSaleExpressCompanyService.updatePostSaleExpressCompany(postSaleExpressCompany));
    }

    /**
     * 删除售后快递公司
     */
    @ApiOperation("删除售后快递公司")
    @Log(title = "售后快递公司", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(postSaleExpressCompanyService.deletePostSaleExpressCompanyByIds(ids));
    }
}
