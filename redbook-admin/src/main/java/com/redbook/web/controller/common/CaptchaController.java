package com.redbook.web.controller.common;

import com.google.code.kaptcha.Producer;
import com.redbook.common.config.RedBookConfig;
import com.redbook.common.constant.CacheConstants;
import com.redbook.common.constant.Constants;
import com.redbook.common.core.domain.AjaxResultOld;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.sms.ZTSmsSender;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.sign.Base64;
import com.redbook.common.utils.uuid.IdUtils;
import com.redbook.system.service.ISysConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@RestController
public class CaptchaController {
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysConfigService configService;

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResultOld getCode(HttpServletResponse response) throws IOException {
        AjaxResultOld ajax = AjaxResultOld.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled) {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = RedBookConfig.getCaptchaType();
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return AjaxResultOld.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    @GetMapping("/captchaTelephone")
    @ApiOperation("获取短信验证码")
    public AjaxResultOld getSmsCode(String phone) {
        AjaxResultOld ajax = AjaxResultOld.success();
        // 保存验证码信息
        String verifyKey = CacheConstants.CAPTCHA_SMS_CODE_KEY + phone;
        if (redisCache.getCacheObject(verifyKey) != null) {
            return AjaxResultOld.error("验证码已发送，请稍后再试");
        }
        // 生成验证码
        String code = IdentityGenerator.randomString(4);
//        Boolean sendMsg = TencentSmsUtil.sendMsg(phone, TencentSmsTemplate.SMS_CODE.getTemplateId(), code);
        Boolean sendMsg = ZTSmsSender.sendVerificationCode(code,phone);


        if (sendMsg) {
            redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        }
        return ajax;

    }
}
