package com.redbook.web.aop;

import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.SysUser;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.redbook.common.utils.SecurityUtils.getLoginUser;

@Aspect
@Component
public class RequestLimitAspect {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 用于配置每个方法的限流时间
    private final Map<String, Integer> methodLimitTime = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        //这里可以配置需要限速的方法

    }

    @Around("@annotation(com.redbook.web.aop.RequestLimit)")
    public Object requestLimit(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Long userId;
        try {
            SysUser user = getLoginUser().getUser();
            if (user == null || user.getUserId() == null) {
                return joinPoint.proceed(); // 未登录用户不限制
            }
            userId = user.getUserId();
        } catch (Exception e) {
            return joinPoint.proceed(); // 登录信息异常时不拦截
        }
        String methodName = method.getName();
        Integer expireTime = getLimitTimeByMethodName(methodName);
        String lockKey = "lock:limit:" + userId + ":" + methodName;
        Boolean locked = setIfAbsent(lockKey, "locked", expireTime, TimeUnit.MILLISECONDS);
        if (Boolean.TRUE.equals(locked)) {
            return joinPoint.proceed();
        } else {
            return AjaxResult.error("操作过于频繁，请稍后再试");
        }
    }

    private Integer getLimitTimeByMethodName(String methodName) {
        Integer i = methodLimitTime.get(methodName);
        return i == null ? 1500 : i; // 默认 1500 毫秒
    }

    private Boolean setIfAbsent(String key, String value, long expire, TimeUnit unit) {
        String script = "if redis.call('setnx', KEYS[1], ARGV[1]) == 1 then " +
                "return redis.call('expire', KEYS[1], ARGV[2]) " +
                "else return 0 end";

        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
        Long result = redisTemplate.execute(redisScript, Collections.singletonList(key), value, String.valueOf(unit.toSeconds(expire)));
        return result != null && result == 1L;
    }
}
