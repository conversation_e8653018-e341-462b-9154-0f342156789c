package com.redbook.listener;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.system.domain.*;
import com.redbook.system.mapper.ActivityUserCoinRecordMapper;
import com.redbook.system.mapper.IMemberRenewDao;
import com.redbook.system.mapper.UserDeferredRecordsMapper;
import com.redbook.system.mapper.UserInfoMapper;
import com.redbook.system.mq.QueueConstants;
import com.redbook.system.service.IAgentService;
import com.redbook.system.util.DateUtil;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RocketMQMessageListener(topic = QueueConstants.SMALL_DCW_COIN_EXCHANGE_MESSAGE, consumerGroup = QueueConstants.CONSUMER_GROUP)
@Component
public class SmallDcwCoinExchangeConsumer implements RocketMQListener<String> {
    private static final Logger log = LoggerFactory.getLogger(SmallDcwCoinExchangeConsumer.class);
    @Autowired
    private ActivityUserCoinRecordMapper activityUserCoinRecordMapper;
    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private UserDeferredRecordsMapper userDeferredRecordsMapper;
    @Value("${redbookHost.reloadSession}")
    private String reloadSessionUrl;
    @Autowired
    RedisCache redisCache;
    @Autowired
    IMemberRenewDao memberRenewDao;
    @Autowired
    private IAgentService agentService;
    @Override
    @Transactional
    public void onMessage(String s) {
//        log.info("收到消息：{},{}", s,System.currentTimeMillis());
        if (redisCache.addlock("exchangeCoin:", "1", 1000 * 60 * 5)) {
            try {
                boolean excute = true;
                while (excute) {
                    Object object = redisCache.leftPop("activity_small_dcw:exchangeList");
                    if (object != null) {
                        UserCoinExchangeBean userCoinExchangeBean = JSONObject.parseObject(String.valueOf(object), UserCoinExchangeBean.class);
                        ActivityUserCoinRecord activityUserCoinRecord = activityUserCoinRecordMapper.selectActivityUserCoinRecordById(userCoinExchangeBean.getAucrId());
                        if (activityUserCoinRecord != null) {
                            String userId = activityUserCoinRecord.getUserId();
                            UserInfo userInfo = userInfoMapper.selectUserInfoByUserId(userId);
                            int exchangeStage = activityUserCoinRecord.getExchangeStage();
                            String exchangeNo = activityUserCoinRecord.getExchangeNo();
                            Date renewBeforeDate = null;
                            Date renewAfterDate = null;
                            if (userInfo != null) {
                                //1小学/2初中/3高中/4大学/5 出国/11小升初/21初升高
                                switch (exchangeStage) {
                                    case 1:
                                        renewBeforeDate = userInfo.getStage1ExpirationDate();
                                        break;
                                    case 2:
                                        renewBeforeDate = userInfo.getStage2ExpirationDate();
                                        break;
                                    case 3:
                                        renewBeforeDate = userInfo.getStage3ExpirationDate();
                                        break;
                                    case 4:
                                        renewBeforeDate = userInfo.getStage4ExpirationDate();
                                        break;
                                    case 5:
                                        renewBeforeDate = userInfo.getStage5ExpirationDate();
                                        break;
                                    case 11:
                                        renewBeforeDate = userInfo.getStage11ExpirationDate();
                                        break;
                                    case 21:
                                        renewBeforeDate = userInfo.getStage21ExpirationDate();
                                        break;
                                    default:
                                        break;
                                }
                                if (renewBeforeDate == null||renewBeforeDate.before(new Date())) {
                                    renewBeforeDate = new Date();
                                }
                                int addLength=3;
                                renewAfterDate = DateUtil.addMonth(renewBeforeDate, addLength);
                                switch (exchangeStage) {
                                    case 1:
                                        userInfo.setStage1ExpirationDate(renewAfterDate);
                                        break;
                                    case 2:
                                        userInfo.setStage2ExpirationDate(renewAfterDate);
                                        break;
                                    case 3:
                                        userInfo.setStage3ExpirationDate(renewAfterDate);
                                        break;
                                    case 4:
                                        userInfo.setStage4ExpirationDate(renewAfterDate);
                                        break;
                                    case 5:
                                        userInfo.setStage5ExpirationDate(renewAfterDate);
                                        break;
                                    case 11:
                                        userInfo.setStage11ExpirationDate(renewAfterDate);
                                        break;
                                    case 21:
                                        userInfo.setStage21ExpirationDate(renewAfterDate);
                                        break;
                                    default:
                                        break;
                                }

                                if (userInfo.getExpirationDate().getTime() < renewAfterDate.getTime()) {
                                    userInfo.setExpirationDate(renewAfterDate);
                                }
                                userInfoMapper.updateStudentExpire(userInfo);
                                reloadUserSession(userId);

                                activityUserCoinRecord.setExchangeStatus("2");
                                activityUserCoinRecord.setExchangeTime(new Date());
                                activityUserCoinRecord.setExchangeExpirationDateBefore(renewBeforeDate);
                                activityUserCoinRecord.setExchangeExpirationDateAfter(renewAfterDate);
                                activityUserCoinRecordMapper.updateActivityUserCoinRecord(activityUserCoinRecord);

                                //生成续费记录
                                Map<String, Object> renewRecordMap = new HashMap<>();
                                renewRecordMap.put("contractorId", userCoinExchangeBean.getContractorId());//操作人
                                Agent agent = agentService.getByAid(userInfo.getAid());
                                Long agentId = agent.getId();
                                renewRecordMap.put("payAreaId", agentId);
                                renewRecordMap.put("agentId", agentId);
                                renewRecordMap.put("userName", userInfo.getUsername());
                                renewRecordMap.put("mobilePhone", userInfo.getMobilePhone());
                                renewRecordMap.put("memberUserId", userId);
                                renewRecordMap.put("firstRenew", -2);

                                renewRecordMap.put("renewStage", exchangeStage);
                                renewRecordMap.put("renewTimeLen", addLength);
                                renewRecordMap.put("buyTimeLen", addLength+" MONTH");
                                renewRecordMap.put("presentTimeLen", "");
                                renewRecordMap.put("renewBeforeDate", DateUtil.dateToString(renewBeforeDate));
                                renewRecordMap.put("renewAfterDate", DateUtil.dateToString(renewAfterDate));
                                renewRecordMap.put("payMoney", new BigDecimal("0.00"));
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                renewRecordMap.put("payTime", sdf.format(new Date()));
                                String indentNumber = agentId + String.valueOf(System.currentTimeMillis());
                                renewRecordMap.put("indentNumber", indentNumber);
                                renewRecordMap.put("electronicRenewCardNumber", "");
                                renewRecordMap.put("activityContentId", 12);
                                renewRecordMap.put("payExclusiveShopMoney", BigDecimal.ZERO);
                                memberRenewDao.addMemberRenewRecord(renewRecordMap);

                            }

                        }

                    } else {
                        excute = false;
                    }
                }

            } catch (Exception e) {
                log.error("兑换失败", e);
            } finally {
                redisCache.releaseLock("exchangeCoin:", "1");
            }

        }
    }

    /**
     * 重新加载用户信息
     *
     * @param userId
     */
    private void reloadUserSession(String userId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", userId);
        HttpUtil.get(reloadSessionUrl, hashMap);
    }
}
