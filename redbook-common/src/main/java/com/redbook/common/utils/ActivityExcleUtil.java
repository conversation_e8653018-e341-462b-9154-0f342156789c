package com.redbook.common.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

public class ActivityExcleUtil {
    public static boolean exportExcel(List<String[]> rowList, String[] header, String fileName, String sheetName, HttpServletResponse response) {
        try {
            //excel表操作对象
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            //创建excel工作空间
            Sheet sheet = xssfWorkbook.createSheet(sheetName);
            //设置表头内容
            XSSFCellStyle cs = xssfWorkbook.createCellStyle(); // 换行的关键，自定义单元格内容换行规则
            cs.setWrapText(true);
            //列数
            int cellNum = header.length;
            Row headerRow = sheet.createRow(0);
            sheet.setColumnWidth(8,5000);
            for (int i = 0; i < cellNum; i++) {
                XSSFRichTextString text = new XSSFRichTextString(header[i]);
                headerRow.createCell(i).setCellValue(text);
            }
            //设置内容,行数从1开始计数
            int rowNum = 1;
            for (String[] contentRow : rowList) {
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < cellNum; i++) {
                    Cell cell = row.createCell(i);
                    String content = contentRow[i];
                    if (i == 8) {
                        if(content.contains("\n")){
                            row.setHeight((short) 900);
                            cell.setCellStyle(cs);
                        }
                    }
                    cell.setCellValue(contentRow[i]);
//                    row.createCell(i).setCellValue(contentRow[i]);
                }
                rowNum++;
            }
            response.setContentType("application/octet-stream");
//            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName,"UTF-8"));
            response.flushBuffer();
            xssfWorkbook.write(response.getOutputStream());
        } catch (IOException e) {
            return false;
        }

        return true;
    }
}
