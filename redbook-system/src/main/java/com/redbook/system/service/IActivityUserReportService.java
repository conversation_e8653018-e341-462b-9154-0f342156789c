package com.redbook.system.service;

import com.redbook.system.domain.ActivityUserReport;

import java.util.List;

/**
 * 用户报告Service接口
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
public interface IActivityUserReportService {

    /**
     * 查询用户报告列表
     *
     * @param activityUserReport 用户报告
     * @return 用户报告集合
     */
    List<ActivityUserReport> selectActivityUserReportList(ActivityUserReport activityUserReport);

}
