package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleExpressCompany;
import com.redbook.system.mapper.PostSaleExpressCompanyMapper;
import com.redbook.system.service.postSale.IPostSaleExpressCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 售后快递公司Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Service
public class PostSaleExpressCompanyServiceImpl extends ServiceImpl<PostSaleExpressCompanyMapper, PostSaleExpressCompany> implements IPostSaleExpressCompanyService {
    @Autowired
    private PostSaleExpressCompanyMapper postSaleExpressCompanyMapper;


    /**
     * 查询售后快递公司列表
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 售后快递公司
     */
    @Override
    public List<PostSaleExpressCompany> selectPostSaleExpressCompanyList(PostSaleExpressCompany postSaleExpressCompany) {
        return postSaleExpressCompanyMapper.selectPostSaleExpressCompanyList(postSaleExpressCompany);
    }

    /**
     * 新增售后快递公司
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    @Override
    public int insertPostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany) {
        postSaleExpressCompany.setCreateTime(DateUtils.getNowDate());
        postSaleExpressCompany.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleExpressCompanyMapper.insertPostSaleExpressCompany(postSaleExpressCompany);
    }

    /**
     * 修改售后快递公司
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    @Override
    public int updatePostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany) {
        postSaleExpressCompany.setUpdateTime(DateUtils.getNowDate());
        postSaleExpressCompany.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleExpressCompanyMapper.updatePostSaleExpressCompany(postSaleExpressCompany);
    }

    /**
     * 批量删除售后快递公司
     *
     * @param ids 需要删除的售后快递公司主键
     * @return 结果
     */
    @Override
    public int deletePostSaleExpressCompanyByIds(Long[] ids) {
        return postSaleExpressCompanyMapper.deletePostSaleExpressCompanyByIds(ids);
    }

}
