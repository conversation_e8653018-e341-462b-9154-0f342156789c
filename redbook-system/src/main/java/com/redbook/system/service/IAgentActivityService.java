package com.redbook.system.service;

import com.redbook.system.domain.AgentActivity;
import com.redbook.system.domain.old.GoodsBean;
import com.redbook.system.domain.vo.AgentActivityQueryVO;

import java.util.List;
import java.util.Map;

/**
 * 代理商活动Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface IAgentActivityService 
{
    /**
     * 查询代理商活动
     * 
     * @param id 代理商活动主键
     * @return 代理商活动
     */
     AgentActivity selectAgentActivityById(Long id);
     List<GoodsBean> goodsList(Long id);
    boolean offShelf(Integer id,String aid);
    boolean onShelf(Integer id,String aid);
    /**
     * 查询代理商活动列表
     * 
     * @param agentActivityQueryVO 代理商活动
     * @return 代理商活动集合
     */
     List<AgentActivity> selectAgentActivityList(AgentActivityQueryVO agentActivityQueryVO);

    List<Map<String, Object>> listGoodsList(String aid, Integer exclusiveShopId);
    /**
     * 新增代理商活动
     * 
     * @param agentActivity 代理商活动
     * @return 结果
     */
     int insertAgentActivity(AgentActivity agentActivity);

    /**
     * 修改代理商活动
     * 
     * @param agentActivity 代理商活动
     * @return 结果
     */
     int updateAgentActivity(AgentActivity agentActivity);
     int addGoodsList(AgentActivity agentActivity);

    /**
     * 批量删除代理商活动
     * 
     * @param ids 需要删除的代理商活动主键集合
     * @return 结果
     */
     int deleteAgentActivityByIds(Integer[] ids);

    /**
     * 删除代理商活动信息
     * 
     * @param id 代理商活动主键
     * @return 结果
     */
     int deleteAgentActivityById(Integer id);
}
