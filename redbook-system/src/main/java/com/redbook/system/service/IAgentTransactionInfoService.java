package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.system.domain.AgentTransactionInfo;
import com.redbook.system.domain.dto.StatisticsInvoiceInfoDto;
import com.redbook.system.domain.dto.TransactionStatisticsDto;
import com.redbook.system.domain.vo.InvoiceDetailExport;
import com.redbook.system.domain.vo.StatisticsInvoiceInfoVo;
import com.redbook.system.domain.vo.TransactionStatisticsVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理商交易记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface IAgentTransactionInfoService extends IService<AgentTransactionInfo>
{
    /**
     * 查询代理商交易记录
     * 
     * @param id 代理商交易记录主键
     * @return 代理商交易记录
     */
     AgentTransactionInfo selectAgentTransactionInfoById(Long id);

    /**
     * 查询代理商交易记录列表
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 代理商交易记录集合
     */
     List<AgentTransactionInfo> selectAgentTransactionInfoList(AgentTransactionInfo agentTransactionInfo);

    /**
     * 新增代理商交易记录
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 结果
     */
     int insertAgentTransactionInfo(AgentTransactionInfo agentTransactionInfo);

    /**
     * 修改代理商交易记录
     * 
     * @param agentTransactionInfo 代理商交易记录
     * @return 结果
     */
     int updateAgentTransactionInfo(AgentTransactionInfo agentTransactionInfo);

    /**
     * 批量删除代理商交易记录
     * 
     * @param ids 需要删除的代理商交易记录主键集合
     * @return 结果
     */
     int deleteAgentTransactionInfoByIds(Long[] ids);

    /**
     * 删除代理商交易记录信息
     * 
     * @param id 代理商交易记录主键
     * @return 结果
     */
     int deleteAgentTransactionInfoById(Long id);

    boolean addTransactionInfo(Long agentId, String transactionType, TransactionFundType fundType, String indentNumber, BigDecimal money,BigDecimal balance,Integer paymentType,Integer topUpWayId,Integer topUpTypeId,String remark);

    /**
     * 统计可开发票信息
     * @param dto
     * @return
     */
    StatisticsInvoiceInfoVo statisticsInvoiceInfo(StatisticsInvoiceInfoDto dto);

    /**
     * 开发票查询
     * @param dto
     * @return
     */
    List<InvoiceDetailExport> statisticsInvoiceByOrder(List<String> prodOrderIdList);

    /**
     * 代理商交易统计
     */
    PageInfo<TransactionStatisticsVo> transactionStatistics(TransactionStatisticsDto dto);

}
