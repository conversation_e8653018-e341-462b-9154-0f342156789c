package com.redbook.system.service.impl;

import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.domain.entity.AgentInfo;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.exception.base.BaseException;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.StringUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.AgentUser;
import com.redbook.system.domain.UserDeferredRecords;
import com.redbook.system.domain.UserInfo;
import com.redbook.system.domain.UserOtherInfoBean;
import com.redbook.system.domain.dto.DeferredDto;
import com.redbook.system.domain.dto.UserClassChangeDto;
import com.redbook.system.domain.dto.UserDeferredDto;
import com.redbook.system.domain.model.UserBean;
import com.redbook.system.domain.vo.AgentParamVo;
import com.redbook.system.domain.vo.ClassInfo;
import com.redbook.system.domain.vo.TableDataSummaryInfoVo;
import com.redbook.system.domain.vo.TeacherInfo;
import com.redbook.system.mapper.UserInfoMapper;
import com.redbook.system.mq.QueueConstants;
import com.redbook.system.service.*;
import com.redbook.system.util.DateUtil;
import com.redbook.system.util.RemoteUtil;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {
    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    IAgentService agentService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    private UserDeferredRecordsServiceImpl userDeferredRecordsService;
    @Value("${redbookHost.reloadSession}")
    private String reloadSessionUrl;
    @Autowired
    RemoteUtil remoteUtil;
    @Autowired
    RocketMQTemplate rocketMQTemplate;
    @Autowired
    IExclusiveShopService exclusiveShopService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    IAgentUserService agentUserService;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public UserInfo selectUserInfoById(Long id) {
        return userInfoMapper.selectUserInfoById(id);
    }

    @Override
    public UserInfo selectUserInfoByUserId(String userId) {
        return userInfoMapper.selectUserInfoByUserId(userId);
    }

    /**
     * 查询会员列表
     *
     * @param userInfo
     * @return
     */
    @Override
    public TableDataInfo<UserInfo> selectUserInfoList(UserInfo userInfo) {
        List<UserInfo> userInfoList = userInfoMapper.selectUserInfoList(userInfo);
        TableDataInfo<UserInfo> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(userInfoList);
        return rspData;
    }

    @Override
    public TableDataSummaryInfoVo countUserInfoList(UserInfo userInfo) {
        int total = 0;
        StringBuilder sb = new StringBuilder();
        Map<String, Object> stringObjectMap = userInfoMapper.countUserInfoList(userInfo);
        if (stringObjectMap == null || stringObjectMap.size() <= 0) {
            sb.append("会员数量：<span>0</span>个。");
        } else {
            if (userInfo.getMemberType() == null || userInfo.getMemberType() > 0) {
                int svipNum = stringObjectMap.get("svipNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("svipNum").toString());
                int vipNum = stringObjectMap.get("vipNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("vipNum").toString());
                int ordinaryNum = stringObjectMap.get("ordinaryNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("ordinaryNum").toString());
                total += svipNum + vipNum + ordinaryNum;
                sb.append("正式会员：").append("<span>").append(ordinaryNum + vipNum + svipNum).append("</span>").append("个");
                sb.append("（普通会员：").append(ordinaryNum).append("个，");
                sb.append("vip会员：").append(vipNum).append("个，");
                sb.append("超级会员：").append(svipNum).append("个）");
            }
            if (userInfo.getMemberType() == null || userInfo.getMemberType() <= 0) {
                int experienceNum = stringObjectMap.get("experienceNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("experienceNum").toString());
                int pcExperienceNum = stringObjectMap.get("pcExperienceNum") == null ? 0 : Integer.parseInt(stringObjectMap.get("pcExperienceNum").toString());
                total += experienceNum + pcExperienceNum;
                if (sb.length() > 0) {
                    sb.append("；");
                }
                sb.append("体验会员：").append("<span>").append(pcExperienceNum + experienceNum).append("</span>").append("个");
                sb.append("（PC体验会员：").append(pcExperienceNum).append("个，体验会员：").append(experienceNum).append("个）");
            }
            sb.append("。");
        }
        return TableDataSummaryInfoVo.builder().summaryInfo(sb.toString()).total(total).build();
    }

    @Override
    public int selectFormalUserCount(String aid, Integer exclusiveShopId) {
        String key = "FormalUserCount:aid:" + aid + ":exclusiveShopId:" + exclusiveShopId;
        Object object = redisCache.getCacheObject(key);
        if (object != null) {
            return (Integer) object;
        }
        int count = userInfoMapper.selectFormalUserCount(aid, exclusiveShopId);
        redisCache.setCacheObject(key, count, 10, TimeUnit.MINUTES);
        return count;
    }

    @Override
    public int selectExperienceUserCount(String aid, Integer exclusiveShopId) {
        String key = "ExperienceUserCount:aid:" + aid + ":exclusiveShopId:" + exclusiveShopId;
        Object object = redisCache.getCacheObject(key);
        if (object != null) {
            return (Integer) object;
        }
        int count = userInfoMapper.selectExperienceUserCount(aid, exclusiveShopId);
        redisCache.setCacheObject(key, count, 10, TimeUnit.MINUTES);
        return count;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param userInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertUserInfo(UserInfo userInfo) {
        return userInfoMapper.insertUserInfo(userInfo);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param userInfo 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateUserInfo(UserInfo userInfo) {
        return userInfoMapper.updateUserInfo(userInfo);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUserInfoByIds(Long[] ids) {
        return userInfoMapper.deleteUserInfoByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUserInfoById(Long id) {
        return userInfoMapper.deleteUserInfoById(id);
    }


    @Override
    public Map<String, Object> getTydRenewRecord(String userId, Integer pageNum, Integer pageSize) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> tydRenewRecord = userInfoMapper.getTydRenewRecord(userId);
        if (!CollectionUtils.isEmpty(tydRenewRecord)) {
            tydRenewRecord.forEach(item -> {
                if (item.get("fundType") == null) {
                    item.put("fundType", "");
                } else {
                    String fundType = item.get("fundType").toString();
                    fundType = fundType.replace("11", "会员款代金券").replace("10", "会员款");
                    item.put("fundType", fundType);
                }
                if(item.containsKey("buyTimeLen")&&item.containsKey("activityContentId")&&item.get("activityContentId")!=null){
                    String buyTimeLenStr = String.valueOf(item.get("buyTimeLen"));
                    if (buyTimeLenStr != null) {
                        StringBuilder sb = new StringBuilder(buyTimeLenStr);
                        if (sb.indexOf("DAY") != -1) {
                            sb.replace(sb.indexOf("DAY"), sb.indexOf("DAY") + 3, "天");
                        }
                        if (sb.indexOf("MONTH") != -1) {
                            sb.replace(sb.indexOf("MONTH"), sb.indexOf("MONTH") + 5, "个月");
                        }
                        item.put("renew_time_len", sb.toString());
                    }
                }

            });
        }
        Integer tydRenewRecordCount = userInfoMapper.getTydRenewRecordCount(userId);
        map.put("list", tydRenewRecord);
        map.put("count", tydRenewRecordCount);
        return map;
    }

    @Override
    public String resetPassword(String userId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUserId, userId);
        if (count(queryWrapper) > 0) {
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(userId);
            String password = IdentityGenerator.randomString(6);
            userInfo.setPassword(Md5Utils.hash(password));
            updateUserInfo(userInfo);
            return password;
        }
        return null;
    }

    @Override
    public Map<String, Object> getUserIpAddress(String userId, String aid, Boolean otherProvincesLogin, Integer pageIndex, Integer pageSize) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = null;
        int count = 0;
        if (pageIndex != null) {
            pageIndex = (pageIndex - 1) * pageSize;
        }
        list = userInfoMapper.getUserIpRedBook(userId, aid, otherProvincesLogin, pageIndex, pageSize);
        list.forEach(m -> {
            m.put("agentName", agentService.getAgentName(m.get("aid").toString()));
        });
        count = userInfoMapper.getUserIpRedBookCount(userId, aid, otherProvincesLogin);
        map.put("list", list);
        map.put("count", count);
        return map;
    }


    public Map<String, Object> getStudentBasicInfo(String userId) {
        Map<String, Object> agentStudentBasicInfo = userInfoMapper.getAgentStudentBasicInfo(userId);
        return agentStudentBasicInfo;
    }

    @Override
    public boolean updateStudentInfo(UserInfo user) {
        boolean flag = updateUserInfo(user) > 0;
        UserOtherInfoBean userOtherInfo = userInfoMapper.getUserOtherInfo(user.getUserId());
        if (userOtherInfo == null) {
            userInfoMapper.insertUserOtherInfo(user.getOtherInfoBean());
        } else {
            userInfoMapper.updateUserOtherInfo(user.getOtherInfoBean());
        }
        return flag;
    }

    @Override
    public List<AgentInfo> getAgentList() {
        AgentParamVo params = sysUserService.getParams(null);
        List<AgentInfo> agentInfoList = params.getAgentInfoList();
        return agentInfoList;
    }

    @Override
    public List<TeacherInfo> getTeacherList(String aid, Integer exclusiveShopId) {
        return userInfoMapper.getTeacherList(aid, exclusiveShopId);
    }

    @Override
    public List<ClassInfo> getClassList(String userId) {
        return userInfoMapper.getClassList(userId);
    }

    @Override
    public boolean updateStudentClass(UserClassChangeDto dto) {
        if (StringUtils.isBlank(dto.getAid()) && dto.getExclusiveShopId() == null) {
            return false;
        }
        LambdaUpdateWrapper<UserInfo> userInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        userInfoLambdaUpdateWrapper.in(UserInfo::getUserId, dto.getUserId());
        //班级ID不能为空
        if (StringUtils.isBlank(dto.getClassId())) {
            dto.setClassId("0");
        }
        userInfoLambdaUpdateWrapper.set(UserInfo::getClassId, dto.getClassId());
        userInfoLambdaUpdateWrapper.set(UserInfo::getTeachId, dto.getTeacherId());
        if (dto.getTeacherId() != null) {
            AgentUser agentUser = agentUserService.selectAgentUserByUserId(dto.getTeacherId());
            userInfoLambdaUpdateWrapper.set(UserInfo::getExclusiveShopId, agentUser.getExclusiveShopId());
            userInfoLambdaUpdateWrapper.set(UserInfo::getAid, agentUser.getAid());
        } else {
            userInfoLambdaUpdateWrapper.set(UserInfo::getExclusiveShopId, dto.getExclusiveShopId());
            if (dto.getExclusiveShopId() != null) {
                userInfoLambdaUpdateWrapper.set(UserInfo::getAid, agentService.selectAgentById(exclusiveShopService.selectExclusiveShopById(dto.getExclusiveShopId()).getAgentId()).getAid());
            } else {
                userInfoLambdaUpdateWrapper.set(UserInfo::getAid, dto.getAid());
            }
        }
        boolean update = update(userInfoLambdaUpdateWrapper);
        if (update && StringUtils.isNotBlank(dto.getClassId())) {
            dto.getUserId().forEach(userId -> {
                rocketMQTemplate.convertAndSend(QueueConstants.STUDENT_CLASS_CHANGE, userId);
            });
        }
        return update;
    }

    @Override
    public boolean updateStudentAgentByTeacher(String teachId, String aid, Integer exclusiveShopId) {
        boolean result = userInfoMapper.updateStudentAgentByTeacher(teachId, aid, exclusiveShopId) > 0;
        return result;
    }

    @Override
    public boolean updateStudentExpire(UserDeferredDto dto) {
        dto.getUserIds().forEach(userId -> {
            UserInfo userInfo = selectUserInfoByUserId(userId);
            UserDeferredRecords build = UserDeferredRecords.builder().userId(userId).userName(userInfo.getUsername()).beforeMemberType(userInfo.getMemberType())
                    .beforeExpirationDate(userInfo.getExpirationDate())
                    .beforeStage1ExpirationDate(userInfo.getStage1ExpirationDate())
                    .beforeStage2ExpirationDate(userInfo.getStage2ExpirationDate())
                    .beforeStage3ExpirationDate(userInfo.getStage3ExpirationDate())
                    .beforeStage4ExpirationDate(userInfo.getStage4ExpirationDate())
                    .beforeStage5ExpirationDate(userInfo.getStage5ExpirationDate())
                    .beforeStage11ExpirationDate(userInfo.getStage11ExpirationDate())
                    .beforeStage21ExpirationDate(userInfo.getStage21ExpirationDate())
                    .memberType(dto.getMemberType()).expirationDate(dto.getExpirationDate())
                    .stage1ExpirationDate(dto.getStage1ExpirationDate())
                    .stage2ExpirationDate(dto.getStage2ExpirationDate())
                    .stage3ExpirationDate(dto.getStage3ExpirationDate())
                    .stage4ExpirationDate(dto.getStage4ExpirationDate())
                    .stage5ExpirationDate(dto.getStage5ExpirationDate())
                    .stage11ExpirationDate(dto.getStage11ExpirationDate())
                    .stage21ExpirationDate(dto.getStage21ExpirationDate())
                    .build();
            build.setRemark(dto.getRemark());
            userDeferredRecordsService.insertUserDeferredRecords(build);
            userInfo.setMemberType(dto.getMemberType());
            Date expirationDate = new Date();
            //计算那个学段的时间是最长的。
            if (dto.getStage1ExpirationDate() != null && expirationDate.getTime() < dto.getStage1ExpirationDate().getTime()) {
                expirationDate = dto.getStage1ExpirationDate();
            }
            if (dto.getStage2ExpirationDate() != null && expirationDate.getTime() < dto.getStage2ExpirationDate().getTime()) {
                expirationDate = dto.getStage2ExpirationDate();
            }
            if (dto.getStage3ExpirationDate() != null && expirationDate.getTime() < dto.getStage3ExpirationDate().getTime()) {
                expirationDate = dto.getStage3ExpirationDate();
            }
            if (dto.getStage4ExpirationDate() != null && expirationDate.getTime() < dto.getStage4ExpirationDate().getTime()) {
                expirationDate = dto.getStage4ExpirationDate();
            }
            if (dto.getStage5ExpirationDate() != null && expirationDate.getTime() < dto.getStage5ExpirationDate().getTime()) {
                expirationDate = dto.getStage5ExpirationDate();
            }
            if (dto.getStage11ExpirationDate() != null && expirationDate.getTime() < dto.getStage11ExpirationDate().getTime()) {
                expirationDate = dto.getStage11ExpirationDate();
            }
            if (dto.getStage21ExpirationDate() != null && expirationDate.getTime() < dto.getStage21ExpirationDate().getTime()) {
                expirationDate = dto.getStage21ExpirationDate();
            }
            userInfo.setExpirationDate(expirationDate);
            userInfo.setStage1ExpirationDate(dto.getStage1ExpirationDate());
            userInfo.setStage2ExpirationDate(dto.getStage2ExpirationDate());
            userInfo.setStage3ExpirationDate(dto.getStage3ExpirationDate());
            userInfo.setStage4ExpirationDate(dto.getStage4ExpirationDate());
            userInfo.setStage5ExpirationDate(dto.getStage5ExpirationDate());
            userInfo.setStage11ExpirationDate(dto.getStage11ExpirationDate());
            userInfo.setStage21ExpirationDate(dto.getStage21ExpirationDate());
            userInfoMapper.updateStudentExpire(userInfo);
            reloadUserSession(userId);
        });
        return true;
    }

    @Override
    public boolean updateStudentExpireByPcUser(UserDeferredDto dto) {
        String userId = dto.getUserId();
        String pcUserId = dto.getPcUserId();
        UserInfo userInfo = selectUserInfoByUserId(userId);
        if (userInfo == null) {
            throw new BaseException(userId + "不存在。");
        }
        if (userInfo.getMemberType() > 0) {
            throw new BaseException(userId + "必须是体验账号。");
        }
        //查询是否是pc旧账号
        UserBean pcUserBean = remoteUtil.getUserByUserId(pcUserId);
        if (pcUserBean == null) {
            throw new BaseException("pc端不存在该账号：" + pcUserId);
        }
        UserInfo pcRedBookUserInfo = selectUserInfoByUserId(pcUserId);
        if (pcRedBookUserInfo == null) {
            throw new BaseException("该pc端账号未在小红本登录过：" + pcUserId);
        }
        if (pcRedBookUserInfo.getIsDelete() == 0) {
            throw new BaseException("该pc端账号在小红本未过期，可以直接续费：" + pcUserId);
        }
        UserDeferredRecords record = UserDeferredRecords.builder().userId(userId).userName(userInfo.getUsername()).
                beforeMemberType(userInfo.getMemberType()).beforeExpirationDate(userInfo.getExpirationDate()).beforeStage1ExpirationDate(userInfo.getStage1ExpirationDate()).beforeStage2ExpirationDate(userInfo.getStage2ExpirationDate()).beforeStage3ExpirationDate(userInfo.getStage3ExpirationDate()).build();
        Integer memberType = 1;
        Map<String, List<Integer>> purchaseProgramList = remoteUtil.getPurchaseProgramList(pcUserBean.getPurchase());
        if (purchaseProgramList == null || purchaseProgramList.get("parentIdList") == null) {
            return false;
        }
        List<Integer> parentIdList = purchaseProgramList.get("parentIdList");
        Date stage1ExpirationDate = null;
        Date stage2ExpirationDate = null;
        Date stage3ExpirationDate = null;
        if (pcUserBean.getPurchase().equals("1,6,") || pcUserBean.getPurchase().equals("2,6,") || pcUserBean.getPurchase().equals("30,6,")) {//小学
            stage1ExpirationDate = pcUserBean.getLastAccessDate();
        } else if (pcUserBean.getPurchase().equals("2,3,6,")) {//小升初
            stage1ExpirationDate = pcUserBean.getLastAccessDate();
            stage2ExpirationDate = pcUserBean.getLastAccessDate();
        } else if (pcUserBean.getPurchase().equals("3,6,") || pcUserBean.getPurchase().equals("4,6,") || pcUserBean.getPurchase().equals("40,6,")) {//初中
            stage2ExpirationDate = pcUserBean.getLastAccessDate();
        } else if (pcUserBean.getPurchase().equals("50,6,")) {//高中
            stage3ExpirationDate = pcUserBean.getLastAccessDate();
        } else {
            if (parentIdList.contains(30)) {
                stage1ExpirationDate = pcUserBean.getLastAccessDate();
            }
            if (parentIdList.contains(40)) {
                stage2ExpirationDate = pcUserBean.getLastAccessDate();
            }
            if (parentIdList.contains(50)) {
                stage3ExpirationDate = pcUserBean.getLastAccessDate();
            }
        }
        //更新到期时间
        userInfo.setMemberType(memberType);
        userInfo.setExpirationDate(pcUserBean.getLastAccessDate());
        userInfo.setStage1ExpirationDate(stage1ExpirationDate);
        userInfo.setStage2ExpirationDate(stage2ExpirationDate);
        userInfo.setStage3ExpirationDate(stage3ExpirationDate);
        //金币也转过来。
        if (pcUserBean.getIntegral() > userInfo.getIntegral()) {
            userInfo.setIntegral(pcUserBean.getIntegral());
        }
        userInfoMapper.updateStudentExpire(userInfo);
        reloadUserSession(userId);
        //把pc旧账号置为已转移
        remoteUtil.updateUserAgentId(pcUserId, userInfo.getAid());
        remoteUtil.updateOldPCUserRedBookMemberType(pcUserId, memberType);
        record.setMemberType(memberType);
        record.setExpirationDate(pcUserBean.getLastAccessDate());
        record.setStage1ExpirationDate(stage1ExpirationDate);
        record.setStage2ExpirationDate(stage2ExpirationDate);
        record.setStage3ExpirationDate(stage3ExpirationDate);
        record.setRemark("将pc端账号" + pcUserId + "到期日期转到" + userId + "中。|" + dto.getRemark());
        userDeferredRecordsService.insertUserDeferredRecords(record);
        return true;
    }

    @Override
    public String importData(MultipartFile file) {
        StringBuilder sb = new StringBuilder();
        //读取 excel
        ExcelUtil<DeferredDto> excelUtil = new ExcelUtil<>(DeferredDto.class);
        try {
            List<DeferredDto> deferredDtos = excelUtil.importExcel(file.getInputStream(), 1);
            for (DeferredDto deferredDto : deferredDtos) {
                String userId = deferredDto.getUserId();
                //查询用户信息
                UserInfo userInfo = selectUserInfoByUserId(userId.trim());
                if (userInfo == null) {
                    sb.append("用户不存在：").append(userId).append("\n");
                    continue;
                }
                //判断要续费的阶段
                String stage = deferredDto.getStage();
                Date expirationDate = null;
                ArrayList<String> ids = new ArrayList<>();
                ids.add(userId);
                UserDeferredDto userDeferredDto = UserDeferredDto.builder().userId(userId).userIds(ids).memberType(userInfo.getMemberType()).expirationDate(userInfo.getExpirationDate())
                        .stage1ExpirationDate(userInfo.getStage1ExpirationDate())
                        .stage2ExpirationDate(userInfo.getStage2ExpirationDate())
                        .stage3ExpirationDate(userInfo.getStage3ExpirationDate())
                        .stage4ExpirationDate(userInfo.getStage4ExpirationDate())
                        .stage5ExpirationDate(userInfo.getStage5ExpirationDate())
                        .stage11ExpirationDate(userInfo.getStage11ExpirationDate())
                        .stage21ExpirationDate(userInfo.getStage21ExpirationDate())
                        .remark("批量导入")
                        .build();
                switch (stage) {
                    case "一阶":
                        expirationDate=userInfo.getStage1ExpirationDate();
                        Date date = addMonth(expirationDate, deferredDto.getRenewTimeLen());
                        userDeferredDto.setStage1ExpirationDate(date);
                        break;
                    case "二阶":
                        expirationDate=userInfo.getStage2ExpirationDate();
                        date = addMonth(expirationDate, deferredDto.getRenewTimeLen());
                        userDeferredDto.setStage2ExpirationDate(date);
                        break;
                    case "三阶":
                        expirationDate=userInfo.getStage3ExpirationDate();
                        date = addMonth(expirationDate, deferredDto.getRenewTimeLen());
                        userDeferredDto.setStage3ExpirationDate(date);
                        break;
                    case "小升初":
                        expirationDate=userInfo.getStage11ExpirationDate();
                        date = addMonth(expirationDate, deferredDto.getRenewTimeLen());
                        userDeferredDto.setStage11ExpirationDate(date);
                        break;
                    case "初升高":
                        expirationDate=userInfo.getStage21ExpirationDate();
                        date = addMonth(expirationDate, deferredDto.getRenewTimeLen());
                        userDeferredDto.setStage21ExpirationDate(date);
                        break;
                    default:
                        //续费阶段不存在
                        sb.append("续费阶段不存在：").append(userId).append("   ").append(stage).append("\n");
                        continue;
                }

                boolean b = updateStudentExpire(userDeferredDto);
                if (!b) {
                    sb.append("续费失败：").append(userId).append("\n");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            // 关闭文件输入流
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return sb.toString();
    }

    private Date addMonth(Date date, int month) {
        // 如果输入的日期为null或早于当前时间，则使用当前时间替换
        if (date == null || date.before(Date.from(Instant.now()))) {
            date = Date.from(Instant.now());
        }

        try {
            return DateUtil.addMonth(date, month);
        } catch (Exception e) {
            // 异常处理：记录日志或抛出自定义异常
            System.err.println("Error occurred while adding months to the date: " + e.getMessage());
            return null; // 或者抛出异常，取决于具体需求
        }
    }
    /**
     * 重新加载用户信息
     *
     * @param userId
     */
    private void reloadUserSession(String userId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", userId);
        HttpUtil.get(reloadSessionUrl, hashMap);
    }

}
