package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleServiceCentre;

import java.util.List;

/**
 * 售后服务中心Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IPostSaleServiceCentreService 
{
    /**
     * 查询售后服务中心
     * 
     * @param id 售后服务中心主键
     * @return 售后服务中心
     */
     PostSaleServiceCentre selectPostSaleServiceCentreById(Long id);

    /**
     * 查询售后服务中心列表
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 售后服务中心集合
     */
     List<PostSaleServiceCentre> selectPostSaleServiceCentreList(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 新增售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
     int insertPostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 修改售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
     int updatePostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 批量删除售后服务中心
     * 
     * @param ids 需要删除的售后服务中心主键集合
     * @return 结果
     */
     int deletePostSaleServiceCentreByIds(Long[] ids);

    /**
     * 删除售后服务中心信息
     * 
     * @param id 售后服务中心主键
     * @return 结果
     */
     int deletePostSaleServiceCentreById(Long id);
}
