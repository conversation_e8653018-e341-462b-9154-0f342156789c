package com.redbook.system.service;

import com.redbook.system.domain.ActivityCodeExchange;

import java.util.List;


public interface IActivityCodeExchangeService {


    List<ActivityCodeExchange> selectActivityCodeExchangeList(ActivityCodeExchange activityCodeExchange);


    /**
     * 修改【请填写功能名称】
     *
     * @param activityCodeExchange 【请填写功能名称】
     * @return 结果
     */
    int updateActivityCodeExchange(ActivityCodeExchange activityCodeExchange);

}
