package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleExpressCompany;

import java.util.List;

/**
 * 售后快递公司Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IPostSaleExpressCompanyService {


    /**
     * 查询售后快递公司列表
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 售后快递公司集合
     */
    List<PostSaleExpressCompany> selectPostSaleExpressCompanyList(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 新增售后快递公司
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    int insertPostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 修改售后快递公司
     *
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    int updatePostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 批量删除售后快递公司
     *
     * @param ids 需要删除的售后快递公司主键集合
     * @return 结果
     */
    int deletePostSaleExpressCompanyByIds(Long[] ids);

}
