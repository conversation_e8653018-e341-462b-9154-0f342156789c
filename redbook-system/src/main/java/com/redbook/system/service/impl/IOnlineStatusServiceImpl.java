package com.redbook.system.service.impl;

import com.redbook.common.utils.RedisManagerOnLine;
import com.redbook.common.utils.TimeUtils;
import com.redbook.system.mapper.OnLineStatusMapper;
import com.redbook.system.service.IOnlineStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IOnlineStatusServiceImpl implements IOnlineStatusService {

    @Autowired
    private OnLineStatusMapper onLineStatusMapper;

    @Override
    public void insertOnlineNumRecord() {
        //获取当前在线人数，redis获取 redis 数据库 2内的 size
        Long onlineNum = RedisManagerOnLine.dbSize();
        String date = TimeUtils.getStringOfCurrentDate();
        onLineStatusMapper.insertOnlineNumRecord(onlineNum == null ? 0 : onlineNum, date);
    }


}
