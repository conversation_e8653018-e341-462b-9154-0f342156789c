package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.sms.ZTSmsSender;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.StockTabletListDto;
import com.redbook.system.domain.dto.StockTabletMoveDto;
import com.redbook.system.domain.vo.StockTabletListVo;
import com.redbook.system.mapper.ProductCategoryMapper;
import com.redbook.system.mapper.ProductMapper;
import com.redbook.system.mapper.ProductOrderMapper;
import com.redbook.system.mapper.ProductSizeInventoryMapper;
import com.redbook.system.service.*;
import com.redbook.system.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 商品订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-09
 */
@Service
public class ProductOrderServiceImpl extends ServiceImpl<ProductOrderMapper, ProductOrder> implements IProductOrderService {
    @Autowired
    private ProductOrderMapper productOrderMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private ProductSizeInventoryMapper productSizeInventoryMapper;
    @Autowired
    private IAgentAccountService accountService;
    @Autowired
    private IAgentTransactionInfoService transactionInfoService;
    @Autowired
    private ProductCategoryMapper productCategoryMapper;
    @Autowired
    RedisCache redisCache;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IRedbookTabletListService redbookTabletListService;
    @Autowired
    IAgentAccountService agentAccountService;
    @Autowired
    private IProductVirtualGoodsListService productVirtualGoodsListService;
    @Autowired
    IExclusiveShopService exclusiveShopService;
    @Autowired
    IProductAgentService productAgentService;
    @Autowired
    IExclusiveShopTransactionInfoService shopTransactionInfoService;
    /**
     * 查询商品订单
     *
     * @param id 商品订单主键
     * @return 商品订单
     */
    @Override
    public ProductOrder selectProductOrderById(Long id) {
        return productOrderMapper.selectProductOrderById(id);
    }

    @Override
    public List<ProductOrder> selectProductOrderByOrderNo(String orderNo) {
        return productOrderMapper.selectProductOrderByOrderNo(orderNo);
    }

    /**
     * 查询商品订单列表
     *
     * @param productOrder 商品订单
     * @return 商品订单
     */
    @Override
    public List<ProductOrder> selectProductOrderList(ProductOrder productOrder) {
        List<ProductOrder> productOrders = productOrderMapper.selectProductOrderList(productOrder);
        productOrders.forEach(po -> {
            if(po.getPayExclusiveShopId()!=null){
                po.setExclusiveShopName(exclusiveShopService.getExclusiveShopName(po.getPayExclusiveShopId()));
            }
            if(productOrder.getOnlyExclusiveShopOrder()!=null&&productOrder.getOnlyExclusiveShopOrder()){
                po.setPayMoney(po.getPayExclusiveShopMoney());
            }
        });
        return productOrders;
    }

    /**
     * 创建商品订单
     *
     * @param productOrder 商品订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertProductOrder(ProductOrder productOrder) {
        Long productId = productOrder.getProductId();
        Product product = productMapper.selectProductById(productId.intValue());
        if (product == null) {
            throw ServiceException.fail("未查询到商品信息");
        }
        Integer status = product.getStatus();
        if (0 == status) {
            throw ServiceException.fail("商品已被下架");
        }
        if (-1 == status) {
            throw ServiceException.fail("商品已被删除");
        }
        ExclusiveShop exclusiveShop = null;
        BigDecimal agentProductPrice = BigDecimal.ZERO;//区域商品价格
        String orderLockName="lock:productOrder:insertProductOrder:"+productOrder.getAgentId()+":"+productOrder.getPayExclusiveShopId();
        if (redisCache.getLock(orderLockName, productId.toString(), 10, 10)) {
            if (productOrder.getPayExclusiveShopId() != null) {
                exclusiveShop = exclusiveShopService.selectExclusiveShopById(productOrder.getPayExclusiveShopId());
                ProductAgent productAgent = productAgentService.selectProductAgent(exclusiveShop.getAgentId(), productId.intValue());
                if (productAgent == null || productAgent.getStatus() != 1) {
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("商品已被下架，无法购买！");
                }
                if (exclusiveShop.getLevel() == 1) {
                    agentProductPrice = productAgent.getShopLevel1Price();
                } else {
                    agentProductPrice = productAgent.getShopLevel2Price();
                }
                productOrder.getPayInfo().setPayAgentId(exclusiveShop.getAgentId());
            }
            Agent agent = agentService.selectAgentById(productOrder.getPayInfo().getPayAgentId());

            String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            //总订购数量
            int totalOrderCount = 0;
            Integer isPresell = product.getIsPresell();
            List<ProductSizeInventory> orderCountDetailList = null;
            //是否可放行
            boolean isPass = false;
            //订购数量
            orderCountDetailList = productOrder.getOrderCountDetailList();
            if (CollectionUtils.isEmpty(orderCountDetailList)) {
                redisCache.releaseLock(orderLockName, productId.toString());
                throw ServiceException.fail("未提交商品规格数量信息");
            }
            totalOrderCount = orderCountDetailList.stream().mapToInt(ProductSizeInventory::getOrderCount).sum();
            if (product.getLimitQuantity() != null && totalOrderCount > product.getLimitQuantity()) {
                redisCache.releaseLock(orderLockName, productId.toString());
                throw ServiceException.fail("商品购买超出限制数量");
            }
            Boolean isGift = false;
            //如果有硬件赠送数量，并且下单的是硬件商品，那么价格为0
            if (productOrder.getPayExclusiveShopId() == null && agent.getGiftCount() != null && agent.getGiftCount() > 0 && agent.getGiftCount() >= totalOrderCount && totalOrderCount % 10 == 0 && product.getCategoryId() == 1) {
                //赠送商品
                product.setPrice(BigDecimal.ZERO);
                agent.setGiftCount(agent.getGiftCount() - totalOrderCount);
                agentService.updateAgent(agent);
                isGift = true;
            }
            if (0 == isPresell) {
                //非预售商品，判断库存情况
                ProductSizeInventory productSizeInventorySearch = new ProductSizeInventory();
                productSizeInventorySearch.setProductId(productId.intValue());
                List<ProductSizeInventory> productSizeInventories = productSizeInventoryMapper.selectProductSizeInventoryList(productSizeInventorySearch);
                if (CollectionUtils.isEmpty(productSizeInventories)) {
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("未查询到商品规格配置信息");
                }
                Map<Integer, ProductSizeInventory> productSizeInventoryMap = productSizeInventories.stream().collect(Collectors.toMap(ProductSizeInventory::getId, Function.identity()));

                for (ProductSizeInventory item : orderCountDetailList) {
                    Integer id = item.getId();
                    String size = item.getSize();
                    ProductSizeInventory productSizeInventory = productSizeInventoryMap.get(id);
                    if (productSizeInventory == null) {
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail(size + "规格信息传递错误");
                    }
                    //订购数量
                    Integer orderCount = item.getOrderCount();
                    //数据库现存数量
                    Integer inventory = productSizeInventory.getInventory();
                    if (orderCount > inventory) {
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail(size + "规格商品库存不足");
                    }
                    isPass = redisCache.addlock(methodName + "-" + id, String.valueOf(id), 2);
                }
            } else {
                //预售商品，没有库存，可直接购买
                isPass = true;
            }
            if (isPass) {
                if (totalOrderCount == 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("请添加购买数量");
                }
                if (productOrder.getPayInfo().getPayPassword() == null) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("支付密码错误");
                }
                if (productOrder.getPayExclusiveShopId() == null) {
                    if (!accountService.checkPayPassword(productOrder.getPayInfo().getPayAgentId(),
                            productOrder.getPayInfo().getPayPassword())) {
                        releaseLock(methodName, orderCountDetailList);
                        throw ServiceException.fail("支付密码错误");
                    }
                } else {
                    //验证专卖店支付密码
                    if (!exclusiveShopService.checkPayPassword(productOrder.getPayExclusiveShopId(), productOrder.getPayInfo().getPayPassword())) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("支付密码错误");
                    }
                }
                //计算商品价格
                BigDecimal totalGoodPrice = product.getPrice().multiply(BigDecimal.valueOf(totalOrderCount));
                //商品 id 为 25 的时候 单次订购20套及以上：135元/套。（2）单次订购10套^19套之间：160元/套。
                if (productId == 25) {
                    if (totalOrderCount >= 20) {
                        totalGoodPrice = BigDecimal.valueOf(135).multiply(BigDecimal.valueOf(totalOrderCount));
                    } else if (totalOrderCount >= 10) {
                        totalGoodPrice = BigDecimal.valueOf(160).multiply(BigDecimal.valueOf(totalOrderCount));
                    }
                }
                //计算邮寄价格
                BigDecimal totalShippingFee = BigDecimal.ZERO;
                Integer shippingType = product.getShippingType();
                if (1 == shippingType) {
                    //单件计费
                    BigDecimal shippingFee = product.getShippingFee();
                    Integer freeShippingCount = product.getFreeShippingCount();
                    if (freeShippingCount == -1 || totalOrderCount < freeShippingCount) {
                        //不免邮、或者免邮但没超过指定免邮数量
                        totalShippingFee = shippingFee.multiply(BigDecimal.valueOf(totalOrderCount));
                    }
                } else if (2 == shippingType) {
                    //多件计费
                    BigDecimal shippingFee = product.getShippingFee();
                    Integer freeShippingCount = product.getFreeShippingCount();
                    if (freeShippingCount == -1 || totalOrderCount < freeShippingCount) {
                        //不免邮、或者免邮但没超过指定免邮数量
                        totalShippingFee = shippingFee;
                    }
                }
                BigDecimal totalPay = totalGoodPrice.add(totalShippingFee);
                if (productOrder.getPayExclusiveShopId() == null && totalPay.compareTo(productOrder.getPayMoney()) != 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("商品价格计算错误，请重新下单！");
                }
                //款项类型，这里都是硬件款
                BigDecimal balance = accountService.getBalance(productOrder.getPayInfo().getPayAgentId(), TransactionFundType.HARDWARE);
                if (balance.compareTo(totalPay) < 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw new ServiceException("区域余额不足，请充值！", HttpStatus.BALANCE_NOT_ENOUGH);
                }
                //订单号
                String orderNo = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
                //专卖店价格
                BigDecimal exclusiveShopTotalPrice = BigDecimal.ZERO;
                if (productOrder.getPayExclusiveShopId() != null) {
                    //计算区域价格
                    exclusiveShopTotalPrice = agentProductPrice.multiply(BigDecimal.valueOf(totalOrderCount)).add(totalShippingFee);
                    if (exclusiveShopTotalPrice.compareTo(productOrder.getPayMoney()) != 0) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("商品价格计算错误，请重新下单！");
                    }
                    if (exclusiveShop.getAccountBalance().compareTo(exclusiveShopTotalPrice) < 0) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw new ServiceException("专卖店余额不足，请充值！", HttpStatus.BALANCE_NOT_ENOUGH);
                    }
                    //更新专卖店余额
                    BigDecimal accountBalance = exclusiveShop.getAccountBalance();
                    BigDecimal surplusBalance = accountBalance.subtract(exclusiveShopTotalPrice);
                    //更新账户余额
                    exclusiveShopService.updateExclusiveShop(ExclusiveShop.builder().id(exclusiveShop.getId()).accountBalance(surplusBalance).build());
                    //插入交易记录
                    shopTransactionInfoService.insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo.builder().exclusiveShopId(exclusiveShop.getId())
                            .indentNumber(orderNo).transactionType("订购商品").money(exclusiveShopTotalPrice).paymentType(1).balance(surplusBalance).remark(productOrder.getProductCategoryName() + productOrder.getProductName()).build());
                }
                //更新区域余额
                balance = balance.subtract(totalPay);
                if (!totalPay.equals(BigDecimal.ZERO)) {
                    accountService.updateAccount(TransactionFundType.HARDWARE, productOrder.getPayInfo().getPayAgentId(), balance);
                    //添加代理商交易记录 赠送时候不做处理
                    if (totalPay.compareTo(BigDecimal.ZERO) > 0) {
                        transactionInfoService.addTransactionInfo(productOrder.getPayInfo().getPayAgentId(), "订购商品", TransactionFundType.HARDWARE, orderNo, totalPay, balance, 1, 0, 0, product.getCategoryName() + product.getName());
                    }
                }
                productOrder.setCreateTime(DateUtils.getNowDate());
                productOrder.setOrderNo(orderNo);
                productOrder.setPayMoney(totalPay);
                productOrder.setPayExclusiveShopMoney(exclusiveShopTotalPrice);
                //如果是赠送订单，款项类型为赠送款
                productOrder.setPayMoneyType(isGift ? TransactionFundType.GIFT.code : TransactionFundType.HARDWARE.code);
                productOrder.setCreateTime(new Date());
                productOrder.setUserId(SecurityUtils.getUserId());
                productOrder.setAgentId(productOrder.getPayInfo().getPayAgentId());
                productOrder.setPayExclusiveShopId(productOrder.getPayInfo().getPayExclusiveShopId());
                productOrder.setOrderCount(totalOrderCount);
                StringBuilder stringBuilder = new StringBuilder();
                int orderDetailSize = orderCountDetailList.size();
                for (int i = 0; i < orderCountDetailList.size(); i++) {
                    ProductSizeInventory productSizeInventory = orderCountDetailList.get(i);
                    Integer orderCount = productSizeInventory.getOrderCount();
                    String size = productSizeInventory.getSize();
                    if (orderDetailSize > 1) {
                        if ("defaultSize".equals(size)) {
                            size = "标准规格";
                        }
                        if (i > 0) {
                            stringBuilder.append(",").append(size).append("×").append(orderCount);
                        } else {
                            stringBuilder.append(size).append("×").append(orderCount);
                        }
                    } else {
                        if ("defaultSize".equals(size)) {
                            stringBuilder.append(orderCount);
                        } else {
                            stringBuilder.append(size).append("×").append(orderCount);
                        }
                    }
                }

                productOrder.setOrderCountDetail(stringBuilder.toString());
                productOrder.setProductName(product.getName());
                //产品规格id
                productOrder.setProductCategoryId((long) product.getCategoryId());
                //产品规格名称
                ProductCategory productCategory = productCategoryMapper.selectProductCategoryById(product.getCategoryId());
                productOrder.setProductCategoryName(productCategory != null ? productCategory.getCategory() : "");
                //如果是虚拟商品，自动发货。
                if (product.getVirtualGoods()) {
                    productOrder.setStatus(1);
                    productOrder.setSendProductTime(productOrder.getCreateTime());
                    productOrder.setTrackingNumber("-");
                    productOrder.setTrackingName("自动发货");
                    //将电子商品信息存到硬件id列表中
                    List<ProductVirtualGoodsList> productVirtualGoodsLists = productVirtualGoodsListService.selectProductVirtualGoodsListList(ProductVirtualGoodsList.builder().productId(productId).status(0).build());
                    if (productVirtualGoodsLists.size() < totalOrderCount) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("库存不足，请联系客服经理或市场经理！");
                    }
                    StringBuilder virtualGoodsKeys = new StringBuilder();
                    for (int i = 0; i < totalOrderCount; i++) {
                        ProductVirtualGoodsList productVirtualGoodsList = productVirtualGoodsLists.get(i);
                        productVirtualGoodsList.setStatus(1);
                        productVirtualGoodsList.setOrderNo(orderNo);
                        productVirtualGoodsListService.updateProductVirtualGoodsList(productVirtualGoodsList);
                        virtualGoodsKeys.append(productVirtualGoodsList.getGoodsInfo() + ",");
                    }
                    productOrder.setTabletIds(virtualGoodsKeys.toString());
                }
                //插入订单记录
                int i = productOrderMapper.insertProductOrder(productOrder);
                if (i > 0) {
                    //非预售商品，更新库存
                    if (0 == isPresell) {
                        orderCountDetailList.forEach(item -> {
                            productSizeInventoryMapper.updateProductSizeInventoryCount(item.getId(), item.getOrderCount());
                        });
                        releaseLock(methodName, orderCountDetailList);
                    }
                }
                redisCache.releaseLock(orderLockName, productId.toString());
                return i;
            }
        }
        return 0;
    }

    private void releaseLock(String methodName, List<ProductSizeInventory> orderCountDetailList) {
        if (!CollectionUtils.isEmpty(orderCountDetailList)) {
            orderCountDetailList.forEach(item -> {
                redisCache.releaseLock(methodName + "-" + item.getId(), String.valueOf(item.getId()));
            });
        }
    }

    /**
     * 修改商品订单
     *
     * @param productOrder 商品订单
     * @return 结果
     */
    @Override
    public int updateProductOrder(ProductOrder productOrder) {
        productOrder.setUpdateTime(DateUtils.getNowDate());
        ProductOrder order = selectProductOrderById(productOrder.getId());
        Agent agent = agentService.getById(order.getAgentId());
        String tabletIds = productOrder.getTabletIds();
        ArrayList<Integer> ids = new ArrayList<>();
        if (tabletIds != null) {
            String[] split = tabletIds.split(",");
            for (String s : split) {
                RedbookTabletList redbookTabletList = redbookTabletListService.selectRedbookTabletListBySN(s);
                if (redbookTabletList != null) ids.add(redbookTabletList.getId().intValue());
            }
        }
        if (!ids.isEmpty()) {
            redbookTabletListService.stockTabletMove(StockTabletMoveDto.builder().agentId(agent.getId().intValue()).ids(ids).build(), SecurityUtils.getLoginUser().getUsername());
        }
        ZTSmsSender.sendShipmentNotification(order.getOrderNo(), productOrder.getTrackingNumber(), order.getRecieverPhone());
        return productOrderMapper.updateProductOrder(productOrder);
    }

    /**
     * 批量删除商品订单
     *
     * @param ids 需要删除的商品订单主键
     * @return 结果
     */
    @Override
    public int deleteProductOrderByIds(Long[] ids) {
        return productOrderMapper.deleteProductOrderByIds(ids);
    }

    /**
     * 删除商品订单信息
     *
     * @param id 商品订单主键
     * @return 结果
     */
    @Override
    public int deleteProductOrderById(Long id) {
        return productOrderMapper.deleteProductOrderById(id);
    }

    @Override
    @Transactional
    public int cancelProductOrderById(Long id) throws Exception {
        ProductOrder productOrder = selectProductOrderById(id);
        if (productOrder == null) {
            throw ServiceException.fail("订单不存在");
        }
        if (productOrder.getStatus() == 3) {
            throw ServiceException.fail("订单状态不可取消");
        }
        //判断是否被拆分
        List<String> splitProductOrderList = redisCache.getCacheList("splitProductOrderList");
        if(CollectionUtil.isNotEmpty(splitProductOrderList) && splitProductOrderList.contains(productOrder.getOrderNo())){
            throw new Exception("该商品订单已拆分过，不能进行取消！");
        }
        String transactionType = "订单取消";
        String remark = "订单号："+productOrder.getOrderNo();
        //退款
        agentAccountService.refund(productOrder.getOrderNo(),transactionType,remark);
        //专卖店下单，退款给专卖店
        if(productOrder.getPayExclusiveShopId()!=null){
            ExclusiveShop exclusiveShop = exclusiveShopService.selectExclusiveShopById(productOrder.getPayExclusiveShopId());
            BigDecimal payExclusiveShopMoney = productOrder.getPayExclusiveShopMoney();
            BigDecimal accountBalance = exclusiveShop.getAccountBalance().add(payExclusiveShopMoney);
            //更新账户余额
            exclusiveShopService.updateExclusiveShop(ExclusiveShop.builder().id(productOrder.getPayExclusiveShopId()).accountBalance(accountBalance).build());
            //插入交易记录
            shopTransactionInfoService.insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo.builder().exclusiveShopId(productOrder.getPayExclusiveShopId())
                    .indentNumber(productOrder.getOrderNo()).transactionType(transactionType).money(payExclusiveShopMoney).paymentType(0).balance(accountBalance).remark(remark).build());
        }
        return productOrderMapper.cancelProductOrder(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSend(MultipartFile file) throws Exception {
        if(null == file){
            return "no file";
        }
        ExcelUtil<ProductOrderBatchSend> util = new ExcelUtil<ProductOrderBatchSend>(ProductOrderBatchSend.class);
        List<ProductOrderBatchSend> orderList = util.importExcel(file.getInputStream());
        if (orderList == null || orderList.size() == 0) {
            return "no file";
        }
        StringBuilder failStr = new StringBuilder();
        int successNum = 0;
        String tabletIds;
        for (ProductOrderBatchSend order : orderList) {
            try {
                order.setOrderNo(order.getOrderNo().replaceAll("[\\s\\u00A0]+","").trim());
                List<ProductOrder> productOrderList = productOrderMapper.selectProductOrderByOrderNo(order.getOrderNo());

                if (CollectionUtil.isEmpty(productOrderList)) {
                    failStr.append("订单号：'"+order.getOrderNo()+"'不存在；\n");
                }else if (CollectionUtil.isNotEmpty(productOrderList) && productOrderList.size()>1) {
                    failStr.append("订单号：'"+order.getOrderNo()+"'进行了拆单，需要单独发货处理；\n");
                }{
                    ProductOrder productOrder = productOrderList.get(0);
                    if(productOrder.getStatus()!=0){
                        failStr.append("订单号：'"+productOrder.getOrderNo()+"'非”待发货“状态；\n");
                    }else if(null==order.getTrackingName()||order.getTrackingName().length()<=0){
                        failStr.append("订单号：'"+productOrder.getOrderNo()+"'未填写快递公司；\n");
                    }else if(null==order.getTrackingNumber()||order.getTrackingNumber().length()<=0){
                        failStr.append("订单号：'"+productOrder.getOrderNo()+"'未填写快递单号；\n");
                    }else if(!productOrder.getProductName().equals(order.getProductName())){
                        failStr.append("订单号：'"+productOrder.getOrderNo()+"'商品信息与数据库中不一致；\n");
                    }else if(!productOrder.getReciever().equals(order.getReciever())||
                            !productOrder.getRecieverPhone().equals(order.getRecieverPhone())||
                            !productOrder.getRecieverAddess().equals(order.getRecieverAddess())){
                        failStr.append("订单号：'"+productOrder.getOrderNo()+"'收货人信息与数据库中不一致；\n");
                    }else{
                        if(productOrder.getProductCategoryName().equals("硬件")){
                            if(order.getBoxNum().length()<=0&&order.getSn().length()<=0){
                                failStr.append("订单号：'"+productOrder.getOrderNo()+"'未添加SN码或箱号；\n");
                                continue;
                            }
                            tabletIds = "";
                            if(!Strings.isNullOrEmpty(order.getBoxNum())){
                                StockTabletListDto query;
                                for (String boxNum : order.getBoxNum().split(",")) {
                                    if(boxNum.length()>0){
                                        query = new StockTabletListDto();
                                        query.setBoxNum(boxNum);
                                        List<StockTabletListVo> stockTabletList = redbookTabletListService.selectStockTabletList(query);
                                        if(stockTabletList.size()>0){
                                            for (StockTabletListVo t : stockTabletList) {
                                                tabletIds+=t.getSn()+",";
                                            }
                                        }
                                    }
                                }
                            }
                            if(!Strings.isNullOrEmpty(order.getSn())){
                                for (String s : order.getSn().split(",")) {
                                    if(s.length()>0){
                                        RedbookTabletList redbookTablet = redbookTabletListService.selectRedbookTabletListBySN(s);
                                        if (redbookTablet != null) {
                                            tabletIds+=redbookTablet.getSn()+",";
                                        }
                                    }
                                }
                            }
                            if(tabletIds.length()<=0){
                                failStr.append("订单号：'"+productOrder.getOrderNo()+"'添加的SN码或箱号有误；\n");
                                continue;
                            }
                            productOrder.setTabletIds(tabletIds);
                        }
                        productOrder.setTrackingName(order.getTrackingName());
                        productOrder.setTrackingNumber(order.getTrackingNumber());
                        productOrder.setSendProductTime(new Date());
                        productOrder.setStatus(1);
                        updateProductOrder(productOrder);
                        successNum++;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                failStr.append(e.getLocalizedMessage()+"\n");
            }
        }
        return "成功发货订单"+successNum+"个，失败"+(orderList.size()-successNum)+"个。\n"+ failStr;
    }

    @Override
    public String splitOrder(ProdOrderPost prodOrderPost) throws Exception {

        if(CollectionUtil.isEmpty(prodOrderPost.getOrderCountList())){
            throw new Exception("拆分数量为空！");
        }
        ProductOrder productOrder = productOrderMapper.selectProductOrderById(prodOrderPost.getProductOrderId());
        if(ObjectUtil.isNull(productOrder)){
            throw new Exception("商品订单不存在！");
        }
        if(1 != productOrder.getProductCategoryId()){
            throw new Exception("只支持硬件订单拆分！");
        }
        if(0 != productOrder.getStatus()){
            throw new Exception("只支持【待发货】订单拆分！");
        }
        LambdaQueryWrapper<ProductOrder> productOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productOrderLambdaQueryWrapper.eq(ProductOrder::getOrderNo, productOrder.getOrderNo());
        if (count(productOrderLambdaQueryWrapper)>2){
            throw new Exception("该商品订单已拆分过，不可以再次拆分！");
        }
        List<Integer> integerList = prodOrderPost.getOrderCountList().stream().filter(data -> data < 10).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(integerList)){
            throw new Exception("每单拆分数量不能少于10！");
        }
        int sum = prodOrderPost.getOrderCountList().stream().mapToInt(Integer::intValue).sum();
        if(!(productOrder.getOrderCount() >= 20 && sum == productOrder.getOrderCount())){
            throw new Exception("该订单订购数量不能少于20，并且拆分数量要等于订购数量！");
        }
        //计算单价
        BigDecimal price=productOrder.getPayMoney().divide(new BigDecimal(productOrder.getOrderCount()),2, RoundingMode.HALF_UP);
        List<Integer> orderCountList = prodOrderPost.getOrderCountList();
        for(int i =0;i<orderCountList.size();i++){
            productOrder.setOrderCount(orderCountList.get(i));
            productOrder.setOrderCountDetail(String.valueOf(productOrder.getOrderCount()));
            productOrder.setPayMoney(price.multiply(new BigDecimal(orderCountList.get(i))));
            if(i==0){
                productOrderMapper.updateProductOrder(productOrder);
            }else {
                productOrderMapper.insertProductOrder(productOrder);
            }
        }
        return "拆分订单成功";
    }

    @Override
    public List<ProductOrder> queryShopCart(){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<ProductOrder> shopCartOrderList = redisCache.getCacheList("shopCartOrderList:"+loginUser.getUserId());
        if(CollectionUtils.isEmpty(shopCartOrderList)){
            return new ArrayList<>();
        }
        shopCartOrderList.forEach(item -> {
            Product product = productMapper.selectProductById(item.getProductId().intValue());
            item.setProductName(product.getName());
            item.setProductCategoryName(product.getCategoryName());
        });

        return shopCartOrderList.stream().sorted(Comparator.comparing(ProductOrder::getProductSort)).collect(Collectors.toList());
    }
    //根据某个字段去重
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public String addShopCart(ProductOrder productOrder) throws Exception {
        if(ObjectUtil.isNotNull(productOrder)){
            Product product = productMapper.selectProductById(productOrder.getProductId().intValue());
            if(1 == product.getCategoryId()){
                throw new Exception("不支持添加硬件商品");
            }
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<ProductOrder> shopCartOrderList = redisCache.getCacheList("shopCartOrderList:"+loginUser.getUserId());
        if(CollectionUtils.isEmpty(shopCartOrderList)){
            shopCartOrderList = new ArrayList<>();
            productOrder.setProductSort(1);
            shopCartOrderList.add(productOrder);
            redisCache.setCacheList("shopCartOrderList:"+loginUser.getUserId(), shopCartOrderList);
            return "添加购物车成功";
        }
        Boolean flag = false;
        for(ProductOrder prodOrder : shopCartOrderList){
            if(prodOrder.getProductId().equals(productOrder.getProductId())){
                productOrder.setProductSort(prodOrder.getProductSort());
                flag = true;
                break;
            }
        }
        if(!flag){
            productOrder.setProductSort(shopCartOrderList.size()+1);
        }
        //购物车已有该产品，再次添加的话，覆盖之前产品
        List<ProductOrder> newList = shopCartOrderList.stream().filter(data -> !data.getProductId().equals(productOrder.getProductId())).collect(Collectors.toList());
        newList.add(productOrder);
        List<ProductOrder> collect = newList.stream().filter(distinctByKey(s -> s.getProductId())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(collect)){
            redisCache.deleteKeys("shopCartOrderList:"+loginUser.getUserId());
        }else {
            redisCache.deleteKeys("shopCartOrderList:"+loginUser.getUserId());
            redisCache.setCacheList("shopCartOrderList:"+loginUser.getUserId(), collect);
        }
        return "添加购物车成功";
    }

    @Override
    public String delShopCart(Long productId) throws Exception {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<ProductOrder> shopCartOrderList = redisCache.getCacheList("shopCartOrderList:"+loginUser.getUserId());
        List<ProductOrder> collect = shopCartOrderList.stream().filter(data -> !data.getProductId().equals(productId)).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(collect)){
            redisCache.deleteKeys("shopCartOrderList:"+loginUser.getUserId());
        }else {
            redisCache.deleteKeys("shopCartOrderList:"+loginUser.getUserId());
            redisCache.setCacheList("shopCartOrderList:"+loginUser.getUserId(), collect);
        }
        return "从购物车删除成功";
    }

    @Override
    public String submitShopCart(ProductOrder productOrder) throws Exception {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<ProductOrder> shopCartOrderList = redisCache.getCacheList("shopCartOrderList:"+loginUser.getUserId());
        if(CollectionUtils.isEmpty(shopCartOrderList)){
            throw new Exception("购物车为空，无法提交！");
        }
        if(ObjectUtil.isNull(productOrder) || productOrder.getPayInfo() == null || productOrder.getPayInfo().getPayAgentId() == null){
            throw new Exception("支付账户未选择！");
        }
        for(ProductOrder order : shopCartOrderList){
            Product product = productMapper.selectProductById(order.getProductId().intValue());
            if(ObjectUtil.isNotNull(product) && 1 == product.getCategoryId()){
                throw new Exception("不支持硬件商品下单！");
            }
        }

        BigDecimal payMoney = shopCartOrderList.stream().max(Comparator.comparing(ProductOrder::getPayMoney)).get().getPayMoney();

        for(ProductOrder order : shopCartOrderList) {
            Boolean shippingFeeFlag = false;
            if (payMoney.equals(order.getPayMoney())) {
                shippingFeeFlag = true;
            }
            order.setPayInfo(productOrder.getPayInfo());
            order.setReciever(productOrder.getReciever());
            order.setRecieverAddess(productOrder.getRecieverAddess());
            order.setRecieverPhone(productOrder.getRecieverPhone());
            order.setStatus(0);
            order.setUpdateTime(null);
            this.insertShopCartProductOrder(order, shippingFeeFlag);
        }
        redisCache.deleteKeys("shopCartOrderList:"+loginUser.getUserId());
        return "购物车订单提交成功";
    }


    public int insertShopCartProductOrder(ProductOrder productOrder,Boolean shippingFeeFlag) {
        Long productId = productOrder.getProductId();
        Product product = productMapper.selectProductById(productId.intValue());
        if (product == null) {
            throw ServiceException.fail("["+product.getName()+"]"+"未查询到商品信息");
        }
        Integer status = product.getStatus();
        if (0 == status) {
            throw ServiceException.fail("["+product.getName()+"]"+"商品已被下架");
        }
        if (-1 == status) {
            throw ServiceException.fail("["+product.getName()+"]"+"商品已被删除");
        }
        ExclusiveShop exclusiveShop = null;
        BigDecimal agentProductPrice = BigDecimal.ZERO;//区域商品价格
        String orderLockName="lock:productOrder:insertProductOrder:"+productOrder.getAgentId()+":"+productOrder.getPayExclusiveShopId();
        if (redisCache.getLock(orderLockName, productId.toString(), 10, 10)) {
            if (productOrder.getPayExclusiveShopId() != null) {
                exclusiveShop = exclusiveShopService.selectExclusiveShopById(productOrder.getPayExclusiveShopId());
                ProductAgent productAgent = productAgentService.selectProductAgent(exclusiveShop.getAgentId(), productId.intValue());
                if (productAgent == null || productAgent.getStatus() != 1) {
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("["+product.getName()+"]"+"商品已被下架，无法购买！");
                }
                if (exclusiveShop.getLevel() == 1) {
                    agentProductPrice = productAgent.getShopLevel1Price();
                } else {
                    agentProductPrice = productAgent.getShopLevel2Price();
                }
                productOrder.getPayInfo().setPayAgentId(exclusiveShop.getAgentId());
            }
            Agent agent = agentService.selectAgentById(productOrder.getPayInfo().getPayAgentId());

            String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            //总订购数量
            int totalOrderCount = 0;
            Integer isPresell = product.getIsPresell();
            List<ProductSizeInventory> orderCountDetailList = null;
            //是否可放行
            boolean isPass = false;
            //订购数量
            orderCountDetailList = productOrder.getOrderCountDetailList();
            if (CollectionUtils.isEmpty(orderCountDetailList)) {
                redisCache.releaseLock(orderLockName, productId.toString());
                throw ServiceException.fail("["+product.getName()+"]"+"未提交商品规格数量信息");
            }
            totalOrderCount = orderCountDetailList.stream().mapToInt(ProductSizeInventory::getOrderCount).sum();
            if (product.getLimitQuantity() != null && totalOrderCount > product.getLimitQuantity()) {
                redisCache.releaseLock(orderLockName, productId.toString());
                throw ServiceException.fail("["+product.getName()+"]"+"商品购买超出限制数量");
            }
            Boolean isGift = false;
            //如果有硬件赠送数量，并且下单的是硬件商品，那么价格为0
            if (productOrder.getPayExclusiveShopId() == null && agent.getGiftCount() != null && agent.getGiftCount() > 0 && agent.getGiftCount() >= totalOrderCount && totalOrderCount % 10 == 0 && product.getCategoryId() == 1) {
                //赠送商品
                product.setPrice(BigDecimal.ZERO);
                agent.setGiftCount(agent.getGiftCount() - totalOrderCount);
                agentService.updateAgent(agent);
                isGift = true;
            }
            if (0 == isPresell) {
                //非预售商品，判断库存情况
                ProductSizeInventory productSizeInventorySearch = new ProductSizeInventory();
                productSizeInventorySearch.setProductId(productId.intValue());
                List<ProductSizeInventory> productSizeInventories = productSizeInventoryMapper.selectProductSizeInventoryList(productSizeInventorySearch);
                if (CollectionUtils.isEmpty(productSizeInventories)) {
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("["+product.getName()+"]"+"未查询到商品规格配置信息");
                }
                Map<Integer, ProductSizeInventory> productSizeInventoryMap = productSizeInventories.stream().collect(Collectors.toMap(ProductSizeInventory::getId, Function.identity()));

                for (ProductSizeInventory item : orderCountDetailList) {
                    Integer id = item.getId();
                    String size = item.getSize();
                    ProductSizeInventory productSizeInventory = productSizeInventoryMap.get(id);
                    if (productSizeInventory == null) {
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail(size + "["+product.getName()+"]"+"规格信息传递错误");
                    }
                    //订购数量
                    Integer orderCount = item.getOrderCount();
                    //数据库现存数量
                    Integer inventory = productSizeInventory.getInventory();
                    if (orderCount > inventory) {
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail(size + "["+product.getName()+"]"+"规格商品库存不足");
                    }
                    isPass = redisCache.addlock(methodName + "-" + id, String.valueOf(id), 2);
                }
            } else {
                //预售商品，没有库存，可直接购买
                isPass = true;
            }
            if (isPass) {
                if (totalOrderCount == 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("["+product.getName()+"]"+"请添加购买数量");
                }
                if (productOrder.getPayInfo().getPayPassword() == null) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("支付密码错误");
                }
                if (productOrder.getPayExclusiveShopId() == null) {
                    if (!accountService.checkPayPassword(productOrder.getPayInfo().getPayAgentId(),
                            productOrder.getPayInfo().getPayPassword())) {
                        releaseLock(methodName, orderCountDetailList);
                        throw ServiceException.fail("支付密码错误");
                    }
                } else {
                    //验证专卖店支付密码
                    if (!exclusiveShopService.checkPayPassword(productOrder.getPayExclusiveShopId(), productOrder.getPayInfo().getPayPassword())) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("支付密码错误");
                    }
                }
                //计算商品价格
                BigDecimal totalGoodPrice = product.getPrice().multiply(BigDecimal.valueOf(totalOrderCount));
                //商品 id 为 25 的时候 单次订购20套及以上：135元/套。（2）单次订购10套^19套之间：160元/套。
                if (productId == 25) {
                    if (totalOrderCount >= 20) {
                        totalGoodPrice = BigDecimal.valueOf(135).multiply(BigDecimal.valueOf(totalOrderCount));
                    } else if (totalOrderCount >= 10) {
                        totalGoodPrice = BigDecimal.valueOf(160).multiply(BigDecimal.valueOf(totalOrderCount));
                    }
                }

                BigDecimal totalShippingFee = BigDecimal.ZERO;
                if(shippingFeeFlag){
                    //计算邮寄价格
                    Integer shippingType = product.getShippingType();
                    if (1 == shippingType) {
                        //单件计费
                        BigDecimal shippingFee = product.getShippingFee();
                        Integer freeShippingCount = product.getFreeShippingCount();
                        if (freeShippingCount == -1 || totalOrderCount < freeShippingCount) {
                            //不免邮、或者免邮但没超过指定免邮数量
                            totalShippingFee = shippingFee.multiply(BigDecimal.valueOf(totalOrderCount));
                        }
                    } else if (2 == shippingType) {
                        //多件计费
                        BigDecimal shippingFee = product.getShippingFee();
                        Integer freeShippingCount = product.getFreeShippingCount();
                        if (freeShippingCount == -1 || totalOrderCount < freeShippingCount) {
                            //不免邮、或者免邮但没超过指定免邮数量
                            totalShippingFee = shippingFee;
                        }
                    }
                }

                BigDecimal totalPay = totalGoodPrice.add(totalShippingFee);
                if (productOrder.getPayExclusiveShopId() == null && totalPay.compareTo(productOrder.getPayMoney().add(new BigDecimal(productOrder.getFreight()))) != 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw ServiceException.fail("["+product.getName()+"]"+"商品价格计算错误，请重新下单！");
                }
                //款项类型，这里都是硬件款
                BigDecimal balance = accountService.getBalance(productOrder.getPayInfo().getPayAgentId(), TransactionFundType.HARDWARE);
                if (balance.compareTo(totalPay) < 0) {
                    releaseLock(methodName, orderCountDetailList);
                    redisCache.releaseLock(orderLockName, productId.toString());
                    throw new ServiceException("区域余额不足，请充值！", HttpStatus.BALANCE_NOT_ENOUGH);
                }
                //订单号
                String orderNo = DateUtil.dateToString(new Date(), "yyyyMMddHHmmssSSS");
                //专卖店价格
                BigDecimal exclusiveShopTotalPrice = BigDecimal.ZERO;
                if (productOrder.getPayExclusiveShopId() != null) {
                    //计算区域价格
                    exclusiveShopTotalPrice = agentProductPrice.multiply(BigDecimal.valueOf(totalOrderCount)).add(totalShippingFee);
                    if (exclusiveShopTotalPrice.compareTo(productOrder.getPayMoney()) != 0) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("["+product.getName()+"]"+"商品价格计算错误，请重新下单！");
                    }
                    if (exclusiveShop.getAccountBalance().compareTo(exclusiveShopTotalPrice) < 0) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw new ServiceException("专卖店余额不足，请充值！", HttpStatus.BALANCE_NOT_ENOUGH);
                    }
                    //更新专卖店余额
                    BigDecimal accountBalance = exclusiveShop.getAccountBalance();
                    BigDecimal surplusBalance = accountBalance.subtract(exclusiveShopTotalPrice);
                    //更新账户余额
                    exclusiveShopService.updateExclusiveShop(ExclusiveShop.builder().id(exclusiveShop.getId()).accountBalance(surplusBalance).build());
                    //插入交易记录
                    shopTransactionInfoService.insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo.builder().exclusiveShopId(exclusiveShop.getId())
                            .indentNumber(orderNo).transactionType("订购商品").money(exclusiveShopTotalPrice).paymentType(1).balance(surplusBalance).remark(productOrder.getProductCategoryName() + productOrder.getProductName()).build());
                }
                //更新区域余额
                balance = balance.subtract(totalPay);
                if (!totalPay.equals(BigDecimal.ZERO)) {
                    accountService.updateAccount(TransactionFundType.HARDWARE, productOrder.getPayInfo().getPayAgentId(), balance);
                    //添加代理商交易记录 赠送时候不做处理
                    if (totalPay.compareTo(BigDecimal.ZERO) > 0) {
                        transactionInfoService.addTransactionInfo(productOrder.getPayInfo().getPayAgentId(), "订购商品", TransactionFundType.HARDWARE, orderNo, totalPay, balance, 1, 0, 0, product.getCategoryName() + product.getName());
                    }
                }
                productOrder.setCreateTime(DateUtils.getNowDate());
                productOrder.setOrderNo(orderNo);
                productOrder.setPayMoney(totalPay);
                productOrder.setPayExclusiveShopMoney(exclusiveShopTotalPrice);
                //如果是赠送订单，款项类型为赠送款
                productOrder.setPayMoneyType(isGift ? TransactionFundType.GIFT.code : TransactionFundType.HARDWARE.code);
                productOrder.setCreateTime(new Date());
                productOrder.setUserId(SecurityUtils.getUserId());
                productOrder.setAgentId(productOrder.getPayInfo().getPayAgentId());
                productOrder.setPayExclusiveShopId(productOrder.getPayInfo().getPayExclusiveShopId());
                productOrder.setOrderCount(totalOrderCount);
                productOrder.setStatus(0);
                StringBuilder stringBuilder = new StringBuilder();
                int orderDetailSize = orderCountDetailList.size();
                for (int i = 0; i < orderCountDetailList.size(); i++) {
                    ProductSizeInventory productSizeInventory = orderCountDetailList.get(i);
                    Integer orderCount = productSizeInventory.getOrderCount();
                    String size = productSizeInventory.getSize();
                    if (orderDetailSize > 1) {
                        if ("defaultSize".equals(size)) {
                            size = "标准规格";
                        }
                        if (i > 0) {
                            stringBuilder.append(",").append(size).append("×").append(orderCount);
                        } else {
                            stringBuilder.append(size).append("×").append(orderCount);
                        }
                    } else {
                        if ("defaultSize".equals(size)) {
                            stringBuilder.append(orderCount);
                        } else {
                            stringBuilder.append(size).append("×").append(orderCount);
                        }
                    }
                }

                productOrder.setOrderCountDetail(stringBuilder.toString());
                productOrder.setProductName(product.getName());
                //产品规格id
                productOrder.setProductCategoryId((long) product.getCategoryId());
                //产品规格名称
                ProductCategory productCategory = productCategoryMapper.selectProductCategoryById(product.getCategoryId());
                productOrder.setProductCategoryName(productCategory != null ? productCategory.getCategory() : "");
                //如果是虚拟商品，自动发货。
                if (product.getVirtualGoods()) {
                    productOrder.setStatus(1);
                    productOrder.setSendProductTime(productOrder.getCreateTime());
                    productOrder.setTrackingNumber("-");
                    productOrder.setTrackingName("自动发货");
                    //将电子商品信息存到硬件id列表中
                    List<ProductVirtualGoodsList> productVirtualGoodsLists = productVirtualGoodsListService.selectProductVirtualGoodsListList(ProductVirtualGoodsList.builder().productId(productId).status(0).build());
                    if (productVirtualGoodsLists.size() < totalOrderCount) {
                        releaseLock(methodName, orderCountDetailList);
                        redisCache.releaseLock(orderLockName, productId.toString());
                        throw ServiceException.fail("["+product.getName()+"]"+"库存不足，请联系客服经理或市场经理！");
                    }
                    StringBuilder virtualGoodsKeys = new StringBuilder();
                    for (int i = 0; i < totalOrderCount; i++) {
                        ProductVirtualGoodsList productVirtualGoodsList = productVirtualGoodsLists.get(i);
                        productVirtualGoodsList.setStatus(1);
                        productVirtualGoodsList.setOrderNo(orderNo);
                        productVirtualGoodsListService.updateProductVirtualGoodsList(productVirtualGoodsList);
                        virtualGoodsKeys.append(productVirtualGoodsList.getGoodsInfo() + ",");
                    }
                    productOrder.setTabletIds(virtualGoodsKeys.toString());
                }
                productOrder.setStatus(0);
                productOrder.setUpdateTime(null);
                //插入订单记录
                int i = productOrderMapper.insertProductOrder(productOrder);
                if (i > 0) {
                    //非预售商品，更新库存
                    if (0 == isPresell) {
                        orderCountDetailList.forEach(item -> {
                            productSizeInventoryMapper.updateProductSizeInventoryCount(item.getId(), item.getOrderCount());
                        });
                        releaseLock(methodName, orderCountDetailList);
                    }
                }
                redisCache.releaseLock(orderLockName, productId.toString());
                return i;
            }
        }
        return 0;
    }


}
