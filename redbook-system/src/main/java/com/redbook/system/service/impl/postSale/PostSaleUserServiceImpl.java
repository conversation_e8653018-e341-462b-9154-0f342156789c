package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.PostSaleUser;
import com.redbook.system.mapper.PostSaleUserMapper;
import com.redbook.system.service.postSale.IPostSaleUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 小程序用户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleUserServiceImpl extends ServiceImpl<PostSaleUserMapper, PostSaleUser> implements IPostSaleUserService
{
    @Autowired
    private PostSaleUserMapper postSaleUserMapper;

    /**
     * 查询小程序用户信息
     * 
     * @param id 小程序用户信息主键
     * @return 小程序用户信息
     */
    @Override
    public PostSaleUser selectPostSaleUserById(Long id)
    {
        return postSaleUserMapper.selectPostSaleUserById(id);
    }

    /**
     * 查询小程序用户信息列表
     * 
     * @param postSaleUser 小程序用户信息
     * @return 小程序用户信息
     */
    @Override
    public List<PostSaleUser> selectPostSaleUserList(PostSaleUser postSaleUser)
    {
        return postSaleUserMapper.selectPostSaleUserList(postSaleUser);
    }

    /**
     * 新增小程序用户信息
     * 
     * @param postSaleUser 小程序用户信息
     * @return 结果
     */
    @Override
    public int insertPostSaleUser(PostSaleUser postSaleUser)
    {
        postSaleUser.setCreateTime(DateUtils.getNowDate());
        return postSaleUserMapper.insertPostSaleUser(postSaleUser);
    }

    /**
     * 修改小程序用户信息
     * 
     * @param postSaleUser 小程序用户信息
     * @return 结果
     */
    @Override
    public int updatePostSaleUser(PostSaleUser postSaleUser)
    {
        postSaleUser.setUpdateTime(DateUtils.getNowDate());
        return postSaleUserMapper.updatePostSaleUser(postSaleUser);
    }

    /**
     * 批量删除小程序用户信息
     * 
     * @param ids 需要删除的小程序用户信息主键
     * @return 结果
     */
    @Override
    public int deletePostSaleUserByIds(Long[] ids)
    {
        return postSaleUserMapper.deletePostSaleUserByIds(ids);
    }

    /**
     * 删除小程序用户信息信息
     * 
     * @param id 小程序用户信息主键
     * @return 结果
     */
    @Override
    public int deletePostSaleUserById(Long id)
    {
        return postSaleUserMapper.deletePostSaleUserById(id);
    }
}
