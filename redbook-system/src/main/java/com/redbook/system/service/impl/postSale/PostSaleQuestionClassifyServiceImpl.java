package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleQuestionClassify;
import com.redbook.system.mapper.PostSaleQuestionClassifyMapper;
import com.redbook.system.service.postSale.IPostSaleQuestionClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 售后问题分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleQuestionClassifyServiceImpl extends ServiceImpl<PostSaleQuestionClassifyMapper, PostSaleQuestionClassify> implements IPostSaleQuestionClassifyService
{
    @Autowired
    private PostSaleQuestionClassifyMapper postSaleQuestionClassifyMapper;

    /**
     * 查询售后问题分类
     * 
     * @param id 售后问题分类主键
     * @return 售后问题分类
     */
    @Override
    public PostSaleQuestionClassify selectPostSaleQuestionClassifyById(Long id)
    {
        return postSaleQuestionClassifyMapper.selectPostSaleQuestionClassifyById(id);
    }

    /**
     * 查询售后问题分类列表
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 售后问题分类
     */
    @Override
    public List<PostSaleQuestionClassify> selectPostSaleQuestionClassifyList(PostSaleQuestionClassify postSaleQuestionClassify)
    {
        return postSaleQuestionClassifyMapper.selectPostSaleQuestionClassifyList(postSaleQuestionClassify);
    }

    /**
     * 新增售后问题分类
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 结果
     */
    @Override
    public int insertPostSaleQuestionClassify(PostSaleQuestionClassify postSaleQuestionClassify)
    {
        postSaleQuestionClassify.setCreateTime(DateUtils.getNowDate());
        postSaleQuestionClassify.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleQuestionClassifyMapper.insertPostSaleQuestionClassify(postSaleQuestionClassify);
    }

    /**
     * 修改售后问题分类
     * 
     * @param postSaleQuestionClassify 售后问题分类
     * @return 结果
     */
    @Override
    public int updatePostSaleQuestionClassify(PostSaleQuestionClassify postSaleQuestionClassify)
    {
        postSaleQuestionClassify.setUpdateTime(DateUtils.getNowDate());
        postSaleQuestionClassify.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleQuestionClassifyMapper.updatePostSaleQuestionClassify(postSaleQuestionClassify);
    }

    /**
     * 批量删除售后问题分类
     * 
     * @param ids 需要删除的售后问题分类主键
     * @return 结果
     */
    @Override
    public int deletePostSaleQuestionClassifyByIds(Long[] ids)
    {
        return postSaleQuestionClassifyMapper.deletePostSaleQuestionClassifyByIds(ids,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }

    /**
     * 删除售后问题分类信息
     * 
     * @param id 售后问题分类主键
     * @return 结果
     */
    @Override
    public int deletePostSaleQuestionClassifyById(Long id)
    {
        return postSaleQuestionClassifyMapper.deletePostSaleQuestionClassifyById(id,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }
}
