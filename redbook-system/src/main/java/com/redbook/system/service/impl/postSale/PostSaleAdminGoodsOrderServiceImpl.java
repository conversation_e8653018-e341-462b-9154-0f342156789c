package com.redbook.system.service.impl.postSale;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.redbook.common.constant.PostSaleConstants;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SfExpressUtil;
import com.redbook.common.utils.StringUtils;
import com.redbook.postsaleapi.model.RefundResult;
import com.redbook.postsaleapi.model.WechatRefundOrderBean;
import com.redbook.system.config.RefundBridge;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.postSale.applet.PostSaleCreateExpressOrderDto;
import com.redbook.system.domain.dto.postSale.sf.SfCreateOrderMsgData;
import com.redbook.system.domain.dto.postSale.sf.SfPrintWaybillsMsgData;
import com.redbook.system.domain.postsale.PostSaleGoodsBlackMailDomain;
import com.redbook.system.domain.postsale.PostSaleGoodsDomain;
import com.redbook.system.domain.vo.postSale.PostSaleConfigVo;
import com.redbook.system.domain.vo.postSale.sf.SfApiResponse;
import com.redbook.system.domain.vo.postSale.sf.SfCreateOrderApiResultData;
import com.redbook.system.domain.vo.postSale.sf.SfPrintWayBillsResultData;
import com.redbook.system.mapper.PostSaleGoodsOrderDetailMapper;
import com.redbook.system.mapper.PostSaleGoodsOrderMapper;
import com.redbook.system.mapper.PostSaleUserAddressMapper;
import com.redbook.system.service.IProductService;
import com.redbook.system.service.postSale.IPostSaleAdminGoodsOrderService;
import com.redbook.system.service.postSale.IPostSaleGoodsInOutService;
import com.redbook.system.service.postSale.IPostSaleGoodsOrderDetailService;
import com.redbook.system.util.GsonManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-04-21 14:14
 */
@Service
public class PostSaleAdminGoodsOrderServiceImpl implements IPostSaleAdminGoodsOrderService {

    @Autowired
    private PostSaleGoodsOrderMapper postSaleGoodsOrderMapper;
    @Autowired
    private IPostSaleGoodsOrderDetailService goodsOrderDetailService;
    @Autowired
    private IProductService productService;
    @Autowired
    private RefundBridge refundBridge;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IPostSaleGoodsInOutService postSaleGoodsInOutService;
    @Autowired
    private PostSaleGoodsOrderDetailMapper postSaleGoodsOrderDetailMapper;
    @Autowired
    private PostSaleUserAddressMapper postSaleUserAddressMapper;
    @Autowired
    SfExpressUtil sfExpressUtil;
    @Value("${express.sfTemplateCode}")
    private String sfTemplateCode;
    @Autowired
    private PostSaleUserCenterServiceImpl postSaleUserCenterService;
    @Autowired
    Environment env;

    @Override
    public PostSaleGoodsOrder selectById(Long id) {
        PostSaleGoodsOrder postSaleGoodsOrder = postSaleGoodsOrderMapper.selectPostSaleGoodsOrderById(id);
        if (postSaleGoodsOrder != null) {
            PostSaleGoodsOrderDetail postSaleGoodsOrderDetail = new PostSaleGoodsOrderDetail();
            postSaleGoodsOrderDetail.setOrderId(postSaleGoodsOrder.getId());
            List<PostSaleGoodsOrderDetail> postSaleGoodsOrderDetails = goodsOrderDetailService.selectPostSaleGoodsOrderDetailList(postSaleGoodsOrderDetail);
            postSaleGoodsOrderDetails.forEach(item1 -> {
                Product product = productService.selectProductById(item1.getGoodId());
                item1.setProduct(product);
            });
            postSaleGoodsOrder.setGoodsOrderDetailList(postSaleGoodsOrderDetails);
        }
        return postSaleGoodsOrder;
    }

    @Override
    public List<PostSaleGoodsOrder> goodsOrderList(PostSaleGoodsOrder postSaleGoodsOrder) {
        List<PostSaleGoodsOrder> postSaleGoodsOrders = postSaleGoodsOrderMapper.goodsOrderListByAdmin(postSaleGoodsOrder);
        if (CollectionUtils.isNotEmpty(postSaleGoodsOrders)) {
            postSaleGoodsOrders.forEach(item -> {
                PostSaleGoodsOrderDetail postSaleGoodsOrderDetail = new PostSaleGoodsOrderDetail();
                postSaleGoodsOrderDetail.setOrderId(item.getId());
                List<PostSaleGoodsOrderDetail> postSaleGoodsOrderDetails = goodsOrderDetailService.selectPostSaleGoodsOrderDetailList(postSaleGoodsOrderDetail);
                StringBuilder sb = new StringBuilder();
                postSaleGoodsOrderDetails.forEach(item1 -> {
                    //商品：HB键盘-HBKB02P   数量：5
                    sb.append("商品：");
                    sb.append(item1.getGoodName());
                    sb.append("  数量：");
                    sb.append(item1.getGoodNum());
                    sb.append("/n");
                });
                sb.deleteCharAt(sb.length() - 1);
                sb.deleteCharAt(sb.length() - 1);
                item.setOrderDetail(sb.toString());
            });
            return postSaleGoodsOrders;
        }
        return new ArrayList<>();
    }

    @Override
    public List<PostSaleGoodsOrder> paybackList(PostSaleGoodsOrder postSaleGoodsOrder) {
        List<PostSaleGoodsOrder> postSaleGoodsOrders = postSaleGoodsOrderMapper.goodsOrderListByAdmin(postSaleGoodsOrder);
        if (CollectionUtils.isNotEmpty(postSaleGoodsOrders)) {
            postSaleGoodsOrders.forEach(item -> {
                PostSaleGoodsOrderDetail postSaleGoodsOrderDetail = new PostSaleGoodsOrderDetail();
                postSaleGoodsOrderDetail.setOrderId(item.getId());
                List<PostSaleGoodsOrderDetail> postSaleGoodsOrderDetails = goodsOrderDetailService.selectPostSaleGoodsOrderDetailList(postSaleGoodsOrderDetail);
                StringBuilder sb = new StringBuilder();
                postSaleGoodsOrderDetails.forEach(item1 -> {
                    //商品：HB键盘-HBKB02P   数量：5
                    sb.append("商品：");
                    sb.append(item1.getGoodName());
                    sb.append("  数量：");
                    sb.append(item1.getGoodNum());
                    sb.append("/n");
                });
                sb.deleteCharAt(sb.length() - 1);
                sb.deleteCharAt(sb.length() - 1);
                item.setOrderDetail(sb.toString());
            });
        }
        return postSaleGoodsOrders;
    }

    @Override
    public String summaryCount() {
        String str = "累计金额：￥0.00   收款笔数：0笔";
        Map<String, Object> map = postSaleGoodsOrderMapper.summaryCount();
        if (map != null) {
            str = "累计金额：￥{0}   收款笔数：{1}笔";
            BigDecimal totalMoney = (BigDecimal) map.get("totalMoney");
            Long totalNum = (Long) map.get("totalNum");
            str = MessageFormat.format(str, totalMoney, totalNum);
        }
        return str;
    }

    @Transactional
    @Override
    public AjaxResult blackmail(PostSaleGoodsBlackMailDomain blackMailDomain) {
        Map<String, Object> map = new HashMap<>();
        map.put("expressType", blackMailDomain.getExpressType());

        PostSaleGoodsOrder postSaleGoodsOrder = postSaleGoodsOrderMapper.selectPostSaleGoodsOrderById(blackMailDomain.getId());
        if (postSaleGoodsOrder == null) {
            throw new RuntimeException("订单不存在");
        }
        if (!PostSaleConstants.PJ_ORDER_STATUS_WAIT_SEND.equals(postSaleGoodsOrder.getOrderStatus())) {
            throw new RuntimeException("订单状态异常，不是待发货");
        }
        postSaleGoodsOrder.setOrderStatus(PostSaleConstants.PJ_ORDER_STATUS_DONE);
        if ("blackMail".equals(blackMailDomain.getExpressType())) {
            //打单发货
            String orderNo = postSaleGoodsOrder.getOrderNo();
            Integer returnExpressCompanyId = blackMailDomain.getExpressCompanyId();
            if (blackMailDomain.getSendExpressPickTime() == null) {
                blackMailDomain.setSendExpressPickTime(new Date());
            }
            PostSaleCreateExpressOrderDto postSaleCreateExpressOrderDto = PostSaleCreateExpressOrderDto.builder()
                    .expressCompanyId(returnExpressCompanyId)
                    .sendStartTm(blackMailDomain.getSendExpressPickTime())
                    .orderId(orderNo)
                    .bizOrderId(postSaleGoodsOrder.getId().intValue())
                    .postSaleUserAddress(
                            PostSaleUserAddress.builder()
                                    .userAddress(postSaleGoodsOrder.getReceiveUserAddress())
                                    .userName(postSaleGoodsOrder.getReceiveUserName())
                                    .userPhone(postSaleGoodsOrder.getReceiveUserPhone())
                                    .build()).build();
            SfCreateOrderApiResultData sfCreateOrderApiResultData = createSfExpressOrder(postSaleCreateExpressOrderDto);
            if (sfCreateOrderApiResultData == null) {
                throw new RuntimeException("创建顺丰快递单失败");
            }
            if (sfCreateOrderApiResultData != null && sfCreateOrderApiResultData.isSuccess()) {
                SfCreateOrderApiResultData.MsgDataBean msgData = sfCreateOrderApiResultData.getMsgData();
                List<SfCreateOrderApiResultData.MsgDataBean.WaybillNoInfoListBean> waybillNoInfoList = msgData.getWaybillNoInfoList();
                if (CollectionUtils.isNotEmpty(waybillNoInfoList)) {
                    SfCreateOrderApiResultData.MsgDataBean.WaybillNoInfoListBean waybillNoInfoListBean = waybillNoInfoList.get(0);
                    SfPrintWaybillsMsgData sfPrintWaybillsMsgData = new SfPrintWaybillsMsgData();
                    sfPrintWaybillsMsgData.setTemplateCode(sfTemplateCode);
                    sfPrintWaybillsMsgData.setSync(true);
                    List<SfPrintWaybillsMsgData.DocumentsBean> documents = new ArrayList<>();
                    SfPrintWaybillsMsgData.DocumentsBean documentsBean = new SfPrintWaybillsMsgData.DocumentsBean();
                    documentsBean.setMasterWaybillNo(waybillNoInfoListBean.getWaybillNo());
                    //赋值快递单号
                    blackMailDomain.setExpressNo(waybillNoInfoListBean.getWaybillNo());
                    //收件人：用户
                    documentsBean.setToAddress(postSaleGoodsOrder.getReceiveUserAddress());
                    documentsBean.setToPhone(postSaleGoodsOrder.getReceiveUserPhone());
                    documentsBean.setToName(postSaleGoodsOrder.getReceiveUserName());
                    //寄件人：公司
                    PostSaleConfigVo postSaleConfigVo = postSaleUserCenterService.serviceConfig();
                    PostSaleServiceCentre postSaleServiceCentre = postSaleConfigVo.getPostSaleServiceCentre();
                    documentsBean.setFromAddress(postSaleServiceCentre.getServiceCentreAddress());
                    //特殊处理，是联系人，非售后
                    documentsBean.setFromPhone(postSaleServiceCentre.getContactUserPhone());
                    documentsBean.setFromName(postSaleServiceCentre.getServiceCentreName());
                    documents.add(documentsBean);
                    sfPrintWaybillsMsgData.setDocuments(documents);
                    String result = sfExpressUtil.syncPrintWaybills(JSONObject.toJSONString(sfPrintWaybillsMsgData));

                    SfApiResponse bean = null;
                    List<SfPrintWayBillsResultData.PrintWayBillObject.ObjFile> files = new ArrayList<>();
                    if (StringUtils.isNotEmpty(result)) {
                        bean = JSONUtil.toBean(result, SfApiResponse.class);
                        if ("A1000".equals(bean.getApiResultCode())) {
                            String apiResultData = bean.getApiResultData();
                            if (StringUtils.isNotEmpty(apiResultData)) {
                                SfPrintWayBillsResultData sfPrintWayBillsResultData = JSONUtil.toBean(apiResultData, SfPrintWayBillsResultData.class);
                                if (sfPrintWayBillsResultData != null && sfPrintWayBillsResultData.isSuccess()) {
                                    SfPrintWayBillsResultData.PrintWayBillObject obj = sfPrintWayBillsResultData.getObj();
                                    if (obj != null) {
                                        files = obj.getFiles();
                                        map.put("files", files);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }
        //更新快递单号
        postSaleGoodsOrder.setExpressNo(blackMailDomain.getExpressNo());
        postSaleGoodsOrder.setExpressCompany("顺丰快递");
        int i = postSaleGoodsOrderMapper.updatePostSaleGoodsOrder(postSaleGoodsOrder);

        List<PostSaleGoodsOrderDetail> postSaleGoodsOrderDetails = postSaleGoodsOrderDetailMapper.selectByOrderId(postSaleGoodsOrder.getId());
        if (CollectionUtils.isNotEmpty(postSaleGoodsOrderDetails)) {
            for (PostSaleGoodsOrderDetail detail : postSaleGoodsOrderDetails) {
                //插入出库记录
                PostSaleGoodsInOut postSaleGoodsInOut = new PostSaleGoodsInOut();
                postSaleGoodsInOut.setGoodId(Long.valueOf(detail.getGoodId()));
                postSaleGoodsInOut.setIntOutNum(Long.valueOf(detail.getGoodNum()));
                postSaleGoodsInOut.setType(2);
                postSaleGoodsInOutService.insertPostSaleGoodsInOut(postSaleGoodsInOut);
            }
        }
        return AjaxResult.success(map);
    }


    //    @Override
    public SfCreateOrderApiResultData createSfExpressOrder(PostSaleCreateExpressOrderDto postSaleCreateExpressOrderDto) {
        if (postSaleCreateExpressOrderDto == null || postSaleCreateExpressOrderDto.getExpressCompanyId() == null || postSaleCreateExpressOrderDto.getSendStartTm() == null) {
            throw new ServiceException("参数信息为空");
        }
        Integer expressCompanyId = postSaleCreateExpressOrderDto.getExpressCompanyId();
        Date sendStartTm = postSaleCreateExpressOrderDto.getSendStartTm();
        Integer userAddressId = postSaleCreateExpressOrderDto.getUserAddressId();
        SfCreateOrderApiResultData sfCreateOrderApiResultData = null;
        PostSaleUserAddress postSaleUserAddress = null;
        if (userAddressId != null) {
            postSaleUserAddress = postSaleUserAddressMapper.selectPostSaleUserAddressById(Long.valueOf(userAddressId));
            if (postSaleUserAddress == null) {
                throw new ServiceException("未查询到用户地址信息");
            }
        } else {
            postSaleUserAddress = postSaleCreateExpressOrderDto.getPostSaleUserAddress();
        }

        //设置数据报文
        SfCreateOrderMsgData sfCreateOrderMsgData = new SfCreateOrderMsgData();
        sfCreateOrderMsgData.setOrderId(postSaleCreateExpressOrderDto.getOrderId());
        sfCreateOrderMsgData.setBizOrderId(postSaleCreateExpressOrderDto.getBizOrderId());
        //构建收寄双方信息
        buildSfContactInfoList(sfCreateOrderMsgData, postSaleUserAddress);
        //构建托寄物
        buildSfCargoDetails(sfCreateOrderMsgData);
        //上门区间时间
        sfCreateOrderMsgData.setSendStartTm(DateUtils.format(sendStartTm, DateUtils.YYYY_MM_DD_HH_MM_SS));
        String result = sfExpressUtil.createOrder(JSONObject.toJSONString(sfCreateOrderMsgData));
        SfApiResponse bean = null;
        if (StringUtils.isNotEmpty(result)) {
            bean = JSONUtil.toBean(result, SfApiResponse.class);
            if ("A1000".equals(bean.getApiResultCode())) {
                String apiResultData = bean.getApiResultData();
                if (StringUtils.isNotEmpty(apiResultData)) {
                    sfCreateOrderApiResultData = JSONUtil.toBean(apiResultData, SfCreateOrderApiResultData.class);
                }
            }

        }
        return sfCreateOrderApiResultData;
    }


    //构建收寄双方信息
    private void buildSfContactInfoList(SfCreateOrderMsgData sfCreateOrderMsgData, PostSaleUserAddress postSaleUserAddress) {
        PostSaleConfigVo postSaleConfigVo = postSaleUserCenterService.serviceConfig();
        PostSaleServiceCentre postSaleServiceCentre = postSaleConfigVo.getPostSaleServiceCentre();
        List<SfCreateOrderMsgData.ContactInfoListBean> contactInfoList = new ArrayList<>();
        //寄件人
        SfCreateOrderMsgData.ContactInfoListBean contactInfoListBeanSend = new SfCreateOrderMsgData.ContactInfoListBean();
        contactInfoListBeanSend.setContact(postSaleServiceCentre.getServiceCentreName());
        contactInfoListBeanSend.setAddress(postSaleServiceCentre.getServiceCentreAddress());
        contactInfoListBeanSend.setMobile(postSaleServiceCentre.getContactUserPhone());
        contactInfoListBeanSend.setContactType(1);
//        contactInfoListBeanSend.setCounty(postSaleUserAddress.getUserCounty());
//        contactInfoListBeanSend.setCity(postSaleUserAddress.getUserCity());
//        contactInfoListBeanSend.setProvince(postSaleUserAddress.getUserProvince());
        contactInfoList.add(contactInfoListBeanSend);
        //收件人
        SfCreateOrderMsgData.ContactInfoListBean contactInfoListBeanReceive = new SfCreateOrderMsgData.ContactInfoListBean();
        contactInfoListBeanReceive.setContact(postSaleUserAddress.getUserName());
        contactInfoListBeanReceive.setAddress(postSaleUserAddress.getUserAddress());
        contactInfoListBeanReceive.setMobile(postSaleUserAddress.getUserPhone());
        contactInfoListBeanReceive.setContactType(2);
        contactInfoListBeanReceive.setCounty(postSaleUserAddress.getUserCounty());
        contactInfoList.add(contactInfoListBeanReceive);


        sfCreateOrderMsgData.setContactInfoList(contactInfoList);
    }


    //构建托寄物
    private void buildSfCargoDetails(SfCreateOrderMsgData sfCreateOrderMsgData) {
        List<SfCreateOrderMsgData.CargoDetailsBean> cargoDetails = new ArrayList<>();
        if (sfCreateOrderMsgData.getBizOrderId() != null) {
            List<PostSaleGoodsOrderDetail> postSaleGoodsOrderDetails = postSaleGoodsOrderDetailMapper.selectByOrderId(Long.valueOf(sfCreateOrderMsgData.getBizOrderId()));
            if (CollectionUtils.isNotEmpty(postSaleGoodsOrderDetails)) {
                for (PostSaleGoodsOrderDetail item : postSaleGoodsOrderDetails) {
                    SfCreateOrderMsgData.CargoDetailsBean cargoDetailsBean = new SfCreateOrderMsgData.CargoDetailsBean();
                    cargoDetailsBean.setName(item.getGoodName());
                    cargoDetailsBean.setCount(item.getGoodNum());
                    cargoDetailsBean.setUnit("个");
                    cargoDetailsBean.setWeight(1.0);
                    cargoDetailsBean.setAmount(item.getGoodPrice().doubleValue());
                    cargoDetailsBean.setCurrency("CNY");
                    cargoDetailsBean.setSourceArea("CHN");
                    cargoDetails.add(cargoDetailsBean);
                }
            }
        }
        sfCreateOrderMsgData.setCargoDetails(cargoDetails);
    }


    @Override
    public Boolean cancelOrderHandle(Long id) {
        PostSaleGoodsOrder postSaleGoodsOrder = postSaleGoodsOrderMapper.selectPostSaleGoodsOrderById(id);
        if (postSaleGoodsOrder == null) {
            throw new RuntimeException("订单不存在");
        }
        if (!PostSaleConstants.PJ_RFD_ORDER_STATUS_APPLYING.equals(postSaleGoodsOrder.getRefundStatus())) {
            throw new RuntimeException("退款状态异常，不是申请中");
        }
        postSaleGoodsOrder.setRefundStatus(PostSaleConstants.PJ_RFD_ORDER_STATUS_HANDLING);
        int i = postSaleGoodsOrderMapper.updatePostSaleGoodsOrder(postSaleGoodsOrder);
        if (i > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean cancelOrderConfirm(PostSaleGoodsDomain postSaleGoodsDomain) {
        PostSaleGoodsOrder postSaleGoodsOrder = postSaleGoodsOrderMapper.selectPostSaleGoodsOrderById(postSaleGoodsDomain.getId());
        if (postSaleGoodsOrder == null) {
            throw new RuntimeException("订单不存在");
        }
        if (!PostSaleConstants.PJ_RFD_ORDER_STATUS_HANDLING.equals(postSaleGoodsOrder.getRefundStatus())) {
            throw new RuntimeException("退款状态异常，不是审核中");
        }
        postSaleGoodsOrder.setRefundStatus(PostSaleConstants.PJ_RFD_ORDER_STATUS_REFUNDING);
        postSaleGoodsOrder.setRefundConfirmTime(DateUtils.getNowDate());
        int i = postSaleGoodsOrderMapper.updatePostSaleGoodsOrder(postSaleGoodsOrder);
        if (i > 0) {
            //实际给微信发送退款申请
            WechatRefundOrderBean refundOrderBean = new WechatRefundOrderBean();
            refundOrderBean.setOutTradeNo(postSaleGoodsOrder.getOrderNo());
            refundOrderBean.setOutRefundNo(postSaleGoodsOrder.getRefundOrderNo());

            if (isPro()) {
                refundOrderBean.setTotalAmount(postSaleGoodsOrder.getPayMoney());
                if (postSaleGoodsDomain.getRefundMoney() == null) {
                    refundOrderBean.setRefundAmount(postSaleGoodsOrder.getPayMoney());
                }
            }else {
                refundOrderBean.setTotalAmount(new BigDecimal(0.01));
                refundOrderBean.setRefundAmount(new BigDecimal(0.01));
            }

            try {
                redisCache.setCacheObject("refundOrderBean", GsonManager.toJson(refundOrderBean));
                RefundResult refunds = refundBridge.doRefund(refundOrderBean);
                redisCache.setCacheObject("refundResult", refunds.toString());
            } catch (Exception e) {

            }
            return true;
        }
        return false;
    }

    //是否是正式环境
    private boolean isPro() {
        boolean isPro = true;
        String profile = env.getActiveProfiles()[0];
        if (profile.equals("dev") || profile.equals("test")) {
            isPro = false;
        }
        return isPro;
    }
}
