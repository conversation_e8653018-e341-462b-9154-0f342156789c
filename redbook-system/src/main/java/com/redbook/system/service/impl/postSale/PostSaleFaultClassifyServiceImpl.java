package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleFaultClassify;
import com.redbook.system.mapper.PostSaleFaultClassifyMapper;
import com.redbook.system.service.postSale.IPostSaleFaultClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 售后故障分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleFaultClassifyServiceImpl extends ServiceImpl<PostSaleFaultClassifyMapper, PostSaleFaultClassify> implements IPostSaleFaultClassifyService {
    @Autowired
    private PostSaleFaultClassifyMapper postSaleFaultClassifyMapper;

    /**
     * 查询售后故障分类
     *
     * @param id 售后故障分类主键
     * @return 售后故障分类
     */
    @Override
    public PostSaleFaultClassify selectPostSaleFaultClassifyById(Integer id) {
        return postSaleFaultClassifyMapper.selectPostSaleFaultClassifyById(id);
    }

    /**
     * 查询售后故障分类列表
     *
     * @param postSaleFaultClassify 售后故障分类
     * @return 售后故障分类
     */
    @Override
    public List<PostSaleFaultClassify> selectPostSaleFaultClassifyList(PostSaleFaultClassify postSaleFaultClassify) {
        postSaleFaultClassify.setParentId(0);
        postSaleFaultClassify.setIsDelete(0L);
        List<PostSaleFaultClassify> postSaleFaultClassifies = postSaleFaultClassifyMapper.selectPostSaleFaultClassifyList(postSaleFaultClassify);
        Optional.ofNullable(postSaleFaultClassifies).orElse(new ArrayList<>()).forEach(item -> {
            postSaleFaultClassify.setParentId(item.getId());
            item.setChilds(postSaleFaultClassifyMapper.selectPostSaleFaultClassifyList(postSaleFaultClassify));
        });
        return postSaleFaultClassifies;
    }

    /**
     * 新增售后故障分类
     *
     * @param postSaleFaultClassify 售后故障分类
     * @return 结果
     */
    @Override
    public int insertPostSaleFaultClassify(PostSaleFaultClassify postSaleFaultClassify) {
        Integer parentId = postSaleFaultClassify.getParentId();
        if (parentId != null && parentId > 0) {
            PostSaleFaultClassify parent = postSaleFaultClassifyMapper.selectPostSaleFaultClassifyById(parentId);
            if (parent == null) {
                throw new ServiceException("未查询到父级故障信息！");
            }
            if (parent.getParentId() > 0) {
                throw new ServiceException("不能给二级故障添加子级！");
            }
        }
        postSaleFaultClassify.setCreateTime(DateUtils.getNowDate());
        postSaleFaultClassify.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleFaultClassifyMapper.insertPostSaleFaultClassify(postSaleFaultClassify);
    }

    /**
     * 修改售后故障分类
     *
     * @param postSaleFaultClassify 售后故障分类
     * @return 结果
     */
    @Override
    public int updatePostSaleFaultClassify(PostSaleFaultClassify postSaleFaultClassify) {
        postSaleFaultClassify.setUpdateTime(DateUtils.getNowDate());
        postSaleFaultClassify.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleFaultClassifyMapper.updatePostSaleFaultClassify(postSaleFaultClassify);
    }

    /**
     * 批量删除售后故障分类
     *
     * @param ids 需要删除的售后故障分类主键
     * @return 结果
     */
    @Override
    public int deletePostSaleFaultClassifyByIds(Long[] ids) {
        return postSaleFaultClassifyMapper.deletePostSaleFaultClassifyByIds(ids,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }

    /**
     * 删除售后故障分类信息
     *
     * @param id 售后故障分类主键
     * @return 结果
     */
    @Override
    public int deletePostSaleFaultClassifyById(Long id) {
        return postSaleFaultClassifyMapper.deletePostSaleFaultClassifyById(id,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }
}
