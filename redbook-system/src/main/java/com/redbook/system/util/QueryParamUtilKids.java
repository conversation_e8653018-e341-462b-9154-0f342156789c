package com.redbook.system.util;

import com.redbook.common.core.domain.BaseEntity;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IExclusiveShopService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 公共查询参数
 * <AUTHOR>
 * @date 2024/1/15 17:36
 */
@Component
public class QueryParamUtilKids {
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IExclusiveShopService exclusiveShopService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 设置公共查询参数
     * 查询agentId列表（缓存1小时）
     * @param baseEntity
     */
    public void setQueryParam(BaseEntity baseEntity) {
        //店长，只允许看到自己的店
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser.getUser().getRole().getRoleId().intValue()==106){
            baseEntity.setExclusiveShopManagerId(loginUser.getUserId());
            //增减条件判断，只要是代理商不管类型都要限定显示
        }else if (loginUser.getUser().getLimitAgent()||loginUser.getUser().getRole().getRoleId().intValue()==100) {
            baseEntity.setmId(loginUser.getUserId());
        }
        //如果指定了代理商，清空模糊查询
        if(baseEntity.getAgentId()!=null){
            baseEntity.setSearchValue(null);
        }
        if(baseEntity.getmId()!=null || baseEntity.getSearchValue()!=null){
            List<Integer> agentIdList;
            String key = "agentIdList:mId:" + baseEntity.getmId() + ":searchValue:" + baseEntity.getSearchValue()+":agentId:"+baseEntity.getAgentId();
            Object object = redisCache.getCacheObject(key);
            if (object!=null){
                agentIdList = (List<Integer>)object;
            }else{
                agentIdList = agentService.getAgentIdList(baseEntity.getmId(),baseEntity.getSearchValue(),baseEntity.getAgentId());
                redisCache.setCacheObject(key,agentIdList,1, TimeUnit.HOURS);
            }
            //如果没有匹配带代理商，填写一个不存在的值，实现返回空值
            if(agentIdList.isEmpty()){
                agentIdList.add(-99);
            }
            baseEntity.setAgentIdList(agentIdList);
        }
        //查专卖店ID列表（缓存1小时）
        if(baseEntity.getExclusiveShopManagerId()!=null && baseEntity.getExclusiveShopId()==null){
            List<Integer> exclusiveShopIdList;
            String key = "exclusiveShopIdList:mId:" + baseEntity.getmId() + ":searchValue:" + baseEntity.getSearchValue() + ":agentId:" + baseEntity.getAgentId() + ":exclusiveShopManagerId:" + baseEntity.getExclusiveShopManagerId();
            Object object = redisCache.getCacheObject(key);
            if (object!=null){
                exclusiveShopIdList = (List<Integer>)object;
            }else{
                ExclusiveShop exclusiveShop = ExclusiveShop.builder().build();
                BeanUtils.copyProperties(baseEntity, exclusiveShop);
                exclusiveShop.setStatus(0);
                exclusiveShopIdList = exclusiveShopService.getExclusiveShopIdList(exclusiveShop);
                redisCache.setCacheObject(key,exclusiveShopIdList,1, TimeUnit.HOURS);
            }
            baseEntity.setExclusiveShopIdList(exclusiveShopIdList);
        }
    }

    /**
     * 设置公共查询参数
     * 查询aid列表（缓存1小时）
     * @param baseEntity
     */
    public void setQueryParamGetAidList(BaseEntity baseEntity) {
        //店长，只允许看到自己的店
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser.getUser().getRole().getRoleId().intValue()==106){
            baseEntity.setExclusiveShopManagerId(loginUser.getUserId());
        }else if (loginUser.getUser().getLimitAgent()||loginUser.getUser().getRole().getRoleId().intValue()==100) {
            baseEntity.setmId(loginUser.getUserId());
        }
        //如果指定了代理商，清空模糊查询
        if(baseEntity.getAgentId()!=null){
            baseEntity.setSearchValue(null);
        }
        if(baseEntity.getmId()!=null || baseEntity.getSearchValue()!=null){
            List<String> aidList;
            String key = "agentAidList:mId:" + baseEntity.getmId() + ":searchValue:" + baseEntity.getSearchValue()+":agentId:"+baseEntity.getAgentId();
            Object object = redisCache.getCacheObject(key);
            if (object!=null){
                aidList = (List<String>)object;
            }else{
                aidList = agentService.getAgentAidList(baseEntity.getmId(),baseEntity.getSearchValue(),baseEntity.getAgentId());
                redisCache.setCacheObject(key,aidList,1, TimeUnit.HOURS);
            }
            //如果没有匹配带代理商，填写一个不存在的值，实现返回空值
            if(aidList.isEmpty()){
                aidList.add("-99");
            }
            baseEntity.setAidList(aidList);
        }

        //查专卖店ID列表（缓存1小时）
        if(baseEntity.getExclusiveShopManagerId()!=null && baseEntity.getExclusiveShopId()==null){
            List<Integer> exclusiveShopIdList;
            String key = "exclusiveShopIdList:mId:" + baseEntity.getmId() + ":searchValue:" + baseEntity.getSearchValue() + ":agentId:" + baseEntity.getAgentId() + ":exclusiveShopManagerId:" + baseEntity.getExclusiveShopManagerId();
            Object object = redisCache.getCacheObject(key);
            if (object!=null){
                exclusiveShopIdList = (List<Integer>)object;
            }else{
                ExclusiveShop exclusiveShop = ExclusiveShop.builder().build();
                BeanUtils.copyProperties(baseEntity, exclusiveShop);
                exclusiveShop.setStatus(0);
                exclusiveShopIdList = exclusiveShopService.getExclusiveShopIdList(exclusiveShop);
                redisCache.setCacheObject(key,exclusiveShopIdList,1, TimeUnit.HOURS);
            }
            baseEntity.setExclusiveShopIdList(exclusiveShopIdList);
        }
    }
}
