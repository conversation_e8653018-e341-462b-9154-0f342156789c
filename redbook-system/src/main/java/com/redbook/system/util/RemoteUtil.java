package com.redbook.system.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.redbook.system.domain.UserInfo;
import com.redbook.system.domain.model.UserBean;
import com.redbook.system.service.IUserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class RemoteUtil {

    @Value("${hss.pc.host}")
    private String pcUrl;

    @Autowired
    private IUserInfoService userInfoService;

    public Map<String, List<Integer>> getPurchaseProgramList(String s) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("s",s);
        String result = getData("getPurchaseProgramList", hashMap);
        if(StringUtils.isNotEmpty(result)&&!"null".equalsIgnoreCase(result)){
            return JSONObject.parseObject(result,Map.class);
        }
        return null;
    }


    public boolean renewUser(String userId, String toAgentFid, Integer renewStage, String totalRenewTimeLen, Integer memberType) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", userId);
        hashMap.put("toAgentFid", toAgentFid);
        hashMap.put("renewStage", renewStage);
        hashMap.put("totalRenewTimeLen", totalRenewTimeLen);
        hashMap.put("memberType", memberType);
        String result = getData("renewUser", hashMap);
        if(StringUtils.isNotEmpty(result)&&!"null".equalsIgnoreCase(result)){
            return JSONObject.parseObject(result, Boolean.class);
        }
        return false;
    }

    public UserBean getUserByUserId(String s) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId",s);
        String result = getData("getUserByUserId", hashMap);
        UserBean userBean = null;
        if(StringUtils.isNotEmpty(result)&&!"null".equalsIgnoreCase(result)){
            userBean = JSONObject.parseObject(result, UserBean.class);
        }
        if(userBean!=null){
            UserInfo userInfo = userInfoService.selectUserInfoByUserId(s);
            userBean.setRedBookUser(userInfo);
        }
        return userBean;
    }

    public void updateUserAgentId(String userId, String aid) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId",userId);
        hashMap.put("aid",aid);
        HttpUtil.get(pcUrl+"updateUserAgentId", hashMap);
    }


    public void updateOldPCUserRedBookMemberType(String userId, Integer memberType) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId",userId);
        hashMap.put("memberType",memberType);
        HttpUtil.get(pcUrl+"updateUserMemberType", hashMap);
    }


    private String getData(String url, Map<String, Object> params) {
        return HttpUtil.get(pcUrl+url, params);
    }
}
