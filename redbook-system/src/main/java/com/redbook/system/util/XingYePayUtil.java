package com.redbook.system.util;

import cn.hutool.core.util.XmlUtil;
import com.redbook.common.constant.CorefireConfig;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.spring.SpringUtils;
import com.redbook.system.domain.AgentRechargeOrder;
import com.redbook.system.domain.vo.AgentDetailVo;
import com.redbook.system.service.IAgentAccountService;
import com.redbook.system.service.IAgentRechargeOrderService;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IAgentTransactionInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.redbook.common.enums.TransactionFundType.NEW_COUPON;

@Component
public class XingYePayUtil {

    @Autowired
    private IAgentRechargeOrderService agentRechargeOrderService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IAgentAccountService agentAccountService;

    @Autowired
    private IAgentTransactionInfoService transactionInfoService;

    @Value("${xingYe.callback.url}")
    private String callbackUrl;

    @Autowired
    RedisTemplate redisTemplate;

    private static final Logger log = LoggerFactory.getLogger(XingYePayUtil.class);

    private static final String SUFFIX = "REQUEST_";


    public Map createOrder(String tradeNo) {
        SortedMap<String, String> map = new TreeMap<>();
        map.put("method", "mbupay.unifpay.native");
        map.put("notify_url", callbackUrl);
        map.put("out_trade_no", tradeNo);
        map.put("nonce_str", String.valueOf(new Date().getTime()));
        AgentRechargeOrder agentRechargeOrder = agentRechargeOrderService.selectAgentRechargeOrderByOrderId(tradeNo);
        Integer fundType = agentRechargeOrder.getFundType();
        String fundTypeName = "";
        switch (fundType) {
            case 5:
                fundTypeName = "小红本保证金";
                break;
            case 8:
                fundTypeName = "小红本续约款";
                break;
            case 9:
                fundTypeName = "小红本硬件款";
                break;
            case 10:
                fundTypeName = "小红本期末会员款";
                //2025年 4 月 1 日以后不允许充值
                if (LocalDate.now().isAfter(LocalDate.of(2025, 4, 1))){
                    throw ServiceException.fail("2025年 4 月 1 日以后不允许充值期末会员款");
                }
                break;
            case 12:
                fundTypeName = "小红本期初会员款";
                break;
            default:
                throw ServiceException.fail("未知的资金类型");
        }
        map.put("body", fundTypeName);
        String key = CorefireConfig.key_red_book;
        map.put("mch_id", CorefireConfig.mch_id_red_book);
        map.put("appid", CorefireConfig.appid_red_book);
        map.put("store_appid", CorefireConfig.store_appid_red_book);
        AgentDetailVo agentDetail = agentService.getAgentDetail(agentRechargeOrder.getAgentId());
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String attach = "代理商：" + agentDetail.getAgent().getName() + "；经理：" + agentDetail.getAgent().getManagerNames() + "；付款人：" + user.getNickName() + "；汇款类别：" + fundTypeName;
        if(agentRechargeOrder.getRemark()!=null){
            attach+="；备注："+agentRechargeOrder.getRemark();
        }
        map.put("attach", attach);
        BigDecimal total_fee_yuan = agentRechargeOrder.getMoney();
        if (SpringUtils.getActiveProfile().equals("test")) {
            total_fee_yuan = new BigDecimal("0.01");
        }
        map.put("total_fee", String.valueOf(total_fee_yuan.multiply(new BigDecimal(100))));

        Map<String, Object> params = SignUtils.paraFilter(map);
        StringBuilder buf = new StringBuilder((params.size() + 1) * 10);
        SignUtils.buildPayParams(buf, params, false);
        String preStr = buf.toString();
        String sign = MD5.sign(preStr, "&key=" + key, "utf-8").toUpperCase();
        map.put("sign", sign);
        String reqUrl = CorefireConfig.common_url;
        try {
            String result = CorefireHttpPost.connect(reqUrl, map);
            Map<String, Object> resultMap = XmlUtil.xmlToMap(result);
            if (!SignUtils.checkParam(resultMap, key)) {
                throw ServiceException.fail("签名验证失败");
            } else {
                if ("FAIL".equals(resultMap.get("result_code"))) {
                    throw ServiceException.fail(resultMap.get("err_code_des").toString());
                } else if ("SUCCESS".equals(resultMap.get("result_code"))) {
                    result = resultMap.get("code_url").toString();
                }
                resultMap.clear();
                resultMap.put("result", result);
                return resultMap;
            }
        } catch (Exception e) {
            throw ServiceException.fail("请求失败");
        }
    }

    @Transactional
    public void callback(String result) {
        Map<String, Object> map = XmlUtil.xmlToMap(result);
        log.info("通知结果：" + result);
        String key = CorefireConfig.key_red_book;
        if (map.containsKey("sign")) {
            if (!SignUtils.checkParam(map, key)) {
                throw ServiceException.fail("签名错误");
            }
            String return_code = (String) map.get("return_code");
            if ("SUCCESS".equals(return_code)) {
                String transaction_id = map.get("transaction_id").toString();//支付订单号
                String lockKey = SUFFIX + transaction_id;
                if (Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 5, TimeUnit.SECONDS))) {
                    String out_trade_no = map.get("out_trade_no").toString();//商户订单号
                    AgentRechargeOrder agentRechargeOrder = agentRechargeOrderService.selectAgentRechargeOrderByOrderId(out_trade_no);
                    if (agentRechargeOrder == null) {
                        throw ServiceException.fail("订单不存在");
                    }
                    if (agentRechargeOrder.getOrderStatus() == 1) {
                        throw ServiceException.fail("订单已支付");
                    }
                    AgentDetailVo agentDetail = agentService.getAgentDetail(agentRechargeOrder.getAgentId());
                    if (agentDetail == null) {
                        throw ServiceException.fail("代理商不存在");
                    }
                    String method = map.get("method").toString();
                    System.out.println("支付方式：:" + method);
                    int toUpWayId = 0;
                    int payMode = 0;
                    if (method.equals("mbupay.wxpay.jsapi")) {//微信付款
                        toUpWayId = 8;
                        payMode = 1;
                    } else if (method.equals("mbupay.alipay.create")) {//支付宝付款
                        toUpWayId = 7;
                        payMode = 2;
                    } else {
                        toUpWayId = 8;
                        payMode = 1;
                    }
                    agentRechargeOrder.setPayMode(payMode);
                    agentRechargeOrder.setOrderStatus(1);
                    agentRechargeOrder.setTradeNo(transaction_id);
                    int i = agentRechargeOrderService.updateAgentRechargeOrder(agentRechargeOrder);
                    if (i > 0) {
                        //更新代理商资金
                        BigDecimal balance = agentAccountService.getBalance(agentRechargeOrder.getAgentId(), TransactionFundType.getEnumByCode(agentRechargeOrder.getFundType()));
                        BigDecimal money = agentRechargeOrder.getMoney();
                        balance = balance.add(money);
                        agentAccountService.updateAccount(TransactionFundType.getEnumByCode(agentRechargeOrder.getFundType()), agentRechargeOrder.getAgentId(), balance);
                        //添加资金流水
                        transactionInfoService.addTransactionInfo(agentRechargeOrder.getAgentId(), agentRechargeOrder.getGiftMoney()!=null?"限时充值优惠活动":"在线充值", TransactionFundType.getEnumByCode(agentRechargeOrder.getFundType()), agentRechargeOrder.getOrderId(), money, balance, 0, toUpWayId, payMode, agentRechargeOrder.getRemark());
                        //充值单可能存在赠送金额
                        if (agentRechargeOrder.getGiftMoney()!=null){
                            //更新代理商资金
                            BigDecimal couponBalance = agentAccountService.getBalance(agentRechargeOrder.getAgentId(), NEW_COUPON);
                            BigDecimal giftMoney = agentRechargeOrder.getGiftMoney();
                            couponBalance = couponBalance.add(giftMoney);
                            agentAccountService.updateAccount(NEW_COUPON, agentRechargeOrder.getAgentId(), couponBalance);
                            //添加资金流水
                            transactionInfoService.addTransactionInfo(agentRechargeOrder.getAgentId(), "限时充值优惠活动", NEW_COUPON, agentRechargeOrder.getOrderId(), giftMoney, couponBalance, 0, toUpWayId, payMode, agentRechargeOrder.getRemark());
                        }
                    }
                } else {
                    throw ServiceException.fail("订单已处理");
                }
            }
        }

    }


    public static String parseXML(Map<String, String> parameters) {
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"appkey".equals(k)) {
                sb.append("<" + k + ">" + parameters.get(k) + "</" + k + ">\n");
            }
        }
        sb.append("</xml>");
        return sb.toString();
    }
}
