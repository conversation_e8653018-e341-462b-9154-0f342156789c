/**
 * Project Name:payment
 * File Name:SignUtils.java
 * Package Name:cn.swiftpass.utils.payment.sign
 * Date:2014-6-27下午3:22:33
 *
*/

package com.redbook.system.util;

import java.net.URLEncoder;
import java.util.*;



/**
 * ClassName:SignUtils
 * Function: 签名用的工具箱
 * Date:     2014-6-27 下午3:22:33 
 * <AUTHOR>
public class SignUtils {

    /** <一句话功能简述>
     * <功能详细描述>验证返回参数
     * @param params
     * @param key
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static boolean checkParam(Map<String,Object> params,String key){
        boolean result = false;
        if(params.containsKey("sign")){
            String sign = params.get("sign").toString();
            params.remove("sign");
            StringBuilder buf = new StringBuilder((params.size() +1) * 10);
            SignUtils.buildPayParams(buf,params,false);
            String preStr = buf.toString();
            String signRecieve = MD5.sign(preStr, "&key=" + key, "utf-8");
            result = sign.equalsIgnoreCase(signRecieve);
        }
        return result;
    }
    
    /**
     * 过滤参数
     * <AUTHOR> @param sArray
     * @return
     */
    public static Map<String, Object> paraFilter(Map<String, String> sArray) {
        Map<String, Object> result = new HashMap<String, Object>(sArray.size());
        if (sArray == null || sArray.size() <= 0) {
            return result;
        }
        for (String key : sArray.keySet()) {
            String value = sArray.get(key);
            if (value == null || value.equals("") || key.equalsIgnoreCase("sign")) {
                continue;
            }
            result.put(key, value);
        }
        return result;
    }
    
    /** <一句话功能简述>
     * <功能详细描述>将map转成String
     * @param payParams
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String payParamsToString(Map<String, Object> payParams){
        return payParamsToString(payParams,false);
    }
    
    public static String payParamsToString(Map<String, Object> payParams,boolean encoding){
        return payParamsToString(new StringBuilder(),payParams,encoding);
    }
    /**
     * <AUTHOR> @param payParams
     * @return
     */
    public static String payParamsToString(StringBuilder sb,Map<String, Object> payParams,boolean encoding){
        buildPayParams(sb,payParams,encoding);
        return sb.toString();
    }
    
    /**
     * <AUTHOR> @param payParams
     * @return
     */
    public static void buildPayParams(StringBuilder sb,Map<String, Object> payParams,boolean encoding){
        List<String> keys = new ArrayList<String>(payParams.keySet());
        Collections.sort(keys);
        for(String key : keys){
        	String value = payParams.get(key).toString();
            if (value == null || value.equals("") || key.equalsIgnoreCase("sign")) {
                continue;
            }
            sb.append(key).append("=");
            if(encoding){
                sb.append(urlEncode(payParams.get(key).toString()));
            }else{
                sb.append(payParams.get(key));
            }
            sb.append("&");
        }
        sb.setLength(sb.length() - 1);
    }
    
    public static String urlEncode(String str){
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (Throwable e) {
            return str;
        } 
    }
    
    

}

