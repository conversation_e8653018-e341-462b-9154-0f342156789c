package com.redbook.system.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;


public class CorefireHttpPost {
    public static String connect(String reqUrl,SortedMap<String,String> map) throws Exception {
        String res= null;
        try {
            //https单向认证
            HttpURLConnection connection = SSLTrustManager.connect(reqUrl);

            connection.setRequestProperty("Content-Type", "text/xml");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestMethod("POST");
            connection.setUseCaches(false);
            connection.setReadTimeout(30000);

            byte[] data = parseXML(map).getBytes(StandardCharsets.UTF_8);
            OutputStream out = connection.getOutputStream();
            out.write(data);
            StringBuffer receivedData = new StringBuffer();
            InputStreamReader inReader = new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8);
            BufferedReader aReader = new BufferedReader(inReader);
            String aLine;
            while ((aLine = aReader.readLine()) != null) {
                receivedData.append(aLine);
            }
            //Integer statusCode = connection.getResponseCode();
            res = receivedData.toString();
            aReader.close();
            connection.disconnect();

        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return res;
    }

    public static String parseXML(Map<String, String> parameters) {
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"appkey".equals(k)) {
                sb.append("<" + k + ">" + parameters.get(k) + "</" + k + ">\n");
            }
        }
        sb.append("</xml>");
        return sb.toString();
    }
}