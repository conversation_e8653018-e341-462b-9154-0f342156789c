package com.redbook.system.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-26 11:04
 */
public class GsonManager {
    static final Gson gson = (new GsonBuilder()).setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    static final Gson gsonObject = (new GsonBuilder()).registerTypeHierarchyAdapter(Map.class, new ObjectTypeAdapter()).create();

    public GsonManager() {
    }

    public static String toJson(Object obj) {
        return gson.toJson(obj);
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        return gsonObject.fromJson(json, classOfT);
    }

    public static Gson getGsonObject() {
        return gsonObject;
    }
}
