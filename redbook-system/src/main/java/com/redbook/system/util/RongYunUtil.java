//package com.redbook.system.util;
//
//import com.alibaba.fastjson.JSONObject;
//import com.redbook.common.core.redis.RedisCache;
//import com.redbook.common.utils.bean.TokenResultPlus;
//import io.rong.RongCloud;
//import io.rong.methods.user.User;
//import io.rong.models.response.TokenResult;
//import io.rong.models.user.UserModel;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.nio.charset.StandardCharsets;
//import java.security.MessageDigest;
//import java.security.NoSuchAlgorithmException;
//import java.util.Formatter;
//
//@Component
//public class RongYunUtil {
//    @Value("${rongyun.appKey}")
//    private String appKey;
//    @Value("${rongyun.appSecret}")
//    private String appSecret;
//
//    @Autowired
//    RedisCache redisCache;
//
//    /**
//     * @param userModel id、name、portrait三个参数必传
//     * @return Result
//     */
//    public TokenResultPlus getToken(UserModel userModel) throws Exception {
//        String obj = redisCache.getCacheObject("hssCustCareToken" + userModel.getId());
//        if (obj == null) {
//            RongCloud rongCloud = RongCloud.getInstance(appKey, appSecret);
//            User user = rongCloud.user;
//            TokenResult result = user.register(userModel);
//            TokenResultPlus tokenResultPlus = new TokenResultPlus();
//            tokenResultPlus.setTokenResult(result);
//            tokenResultPlus.setAppKey(appKey);
//            if (result.getCode() == 200) {
//                redisCache.setCacheObject("hssCustCareToken", JSONObject.toJSONString(result));
//            }
//            return tokenResultPlus;
//        } else {
//            return JSONObject.parseObject(redisCache.getCacheObject("hssCustCareToken" + userModel.getId()), TokenResultPlus.class);
//        }
//    }
//
//    /**
//     * sha1验证
//     *
//     * @param str
//     * @return
//     */
//    public  String getSha1(String str) {
//        String signature = null;
//        try {
//            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
//            crypt.reset();
//            crypt.update(str.getBytes(StandardCharsets.UTF_8));
//            Formatter formatter = new Formatter();
//            for (byte b : crypt.digest()) {
//                formatter.format("%02x", b);
//            }
//            signature = formatter.toString();
//            formatter.close();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        return signature;
//    }
//
//    public  Boolean check(String appKey1, String nonce, String timestamp, String signature) {
//        try {
//            if (!appKey1.equals(appKey)) {
//                return false;
//            }
//            if (!getSha1(appSecret + nonce + timestamp).equals(signature)) {
//                return false;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return true;
//    }
//
//}
