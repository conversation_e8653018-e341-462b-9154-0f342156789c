package com.redbook.system.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 线索状态枚举
 * <AUTHOR>
 * @date 2024-03-26 15:14
 */
public enum ClueStatusEnum {
    INVALID("无效",0,"灰色"),
    NOT_FOLLOW("未跟进",1,"红色"),
    BE_FOLLOWING("跟进中",2,"黑色"),
    ALREADY_INCITE("已邀约",3,"黑色"),
    ALREADY_STORE("已到店",4,"黑色"),
    ALREADY_EXPERIENCE("已体验",5,"黑色"),
    ALREADY_DEAL("已成交",6,"绿色"),
    ALREADY_RENEW("已续费",7,"绿色"),
    LOST("已流失",8,"红色"),

    ;

    private final String name;
    private final Integer value;
    private final String type;

    private static final List<ClueStatusEnum> list = new ArrayList<>();

    static {
        Collections.addAll(list, ClueStatusEnum.values());
    }

    /**
     * 枚举list
     * @return
     */
    public static List<ClueStatusEnum> getList() {
        return list;
    }

    /**
     * 状态下拉列表
     * @return
     */
    public static List<ClueStatusDO> getStatusList(){
        List<ClueStatusEnum> clueStatusEnumList = getList();
        List<ClueStatusDO> clueStatusDOList = new ArrayList<>();
        ClueStatusDO firstClueStatusDO = new ClueStatusDO();
        firstClueStatusDO.setName("全部");
        firstClueStatusDO.setValue(-1);
        firstClueStatusDO.setType("");
        clueStatusDOList.add(firstClueStatusDO);
        clueStatusEnumList.forEach(item ->{
            ClueStatusDO clueStatusDO = new ClueStatusDO();
            clueStatusDO.setName(item.getName());
            clueStatusDO.setValue(item.getValue());
            clueStatusDO.setType(item.getType());
            clueStatusDOList.add(clueStatusDO);
        });
        return clueStatusDOList;
    }

    @Data
    public static class ClueStatusDO{
        private String name;
        private Integer value;
        private String type;
    }


    ClueStatusEnum(String name, Integer value,String type) {
        this.name = name;
        this.value = value;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public String getType() {
        return type;
    }


}
