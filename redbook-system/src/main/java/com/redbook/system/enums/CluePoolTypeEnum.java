package com.redbook.system.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 线索-线索池枚举
 * <AUTHOR>
 * @date 2024-03-28
 */
public enum CluePoolTypeEnum {

    COMMON_POOL("共有池",1,""),
    FOLLOW_POOL("跟进池",2,""),
    DEAL_POOL("成交池",3,""),

    ;

    private final String name;
    private final Integer value;
    private final String type;

    private static final List<CluePoolTypeEnum> list = new ArrayList<>();

    static {
        Collections.addAll(list, CluePoolTypeEnum.values());
    }

    /**
     * 枚举list
     * @return
     */
    public static List<CluePoolTypeEnum> getList() {
        return list;
    }

    /**
     * 状态下拉列表
     * @return
     */
    public static List<CluePoolTypeDO> getCluePoolTypeList(){
        List<CluePoolTypeEnum> clueStatusEnumList = getList();
        List<CluePoolTypeDO> cluePoolTypeDOList = new ArrayList<>();
        CluePoolTypeDO first = new CluePoolTypeDO();
        first.setName("全部");
        first.setValue(-1);
        first.setType("");
        cluePoolTypeDOList.add(first);
        clueStatusEnumList.forEach(item ->{
            CluePoolTypeDO cluePoolTypeDO = new CluePoolTypeDO();
            cluePoolTypeDO.setName(item.getName());
            cluePoolTypeDO.setValue(item.getValue());
            cluePoolTypeDO.setType(item.getType());
            cluePoolTypeDOList.add(cluePoolTypeDO);
        });
        return cluePoolTypeDOList;
    }

    @Data
    public static class CluePoolTypeDO{
        private String name;
        private Integer value;
        private String type;
    }


    CluePoolTypeEnum(String name, Integer value, String type) {
        this.name = name;
        this.value = value;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public String getType() {
        return type;
    }


}
