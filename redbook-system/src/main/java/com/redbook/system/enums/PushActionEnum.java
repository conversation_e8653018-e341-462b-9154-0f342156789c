package com.redbook.system.enums;

/**
 * 父项目中枚举类
 * <AUTHOR> @date 2024-03-26 11:09
 */
public enum PushActionEnum {
    GO_TASK("强制跳到任务页面"),
    TASK_UPDATE("任务数据更新"),
    GO_CLASS_TRAIN("强制跳到课堂训练页面"),
    GO_CLASS_WORK("强制跳到课后作业页面"),
    CANCEL_TASK_MUST("取消任务强制"),
    CLOSE_CLASS_TRAIN("关闭课堂训练"),
    CLOSE_CLASS_WORK("关闭课后作业"),
    OFFLINE_INTEGRAL_UPDATE("线下金币更新"),
    STUDY_AGAIN("再学一遍"),
    CREATE_YLB_PK("发起友乐邦pk"),
    TEACHER_OPERATE_ORDER("老师操作了订单"),
    TEACHER_MODIFY_SYNC_COURSE("老师修改了同步课程"),
    TEACHER_MODIFY_TASK_COURSE("老师修改了任务课程"),
    TEACHER_MODIFY_TASK_COURSE_ISMUST("老师修改任务课程是否强制"),
    TEACHER_MODIFY_MUST_MODULE("老师修改了必学模块"),
    TEACHER_MODIFY_MUST_MODULE_ISMUST("老师修改了必学模块是否强制"),
    TEACHER_MODIFY_HIGH_PERFORMANCE_MODE_SETTING("老师修改高效闯关模式开关"),
    TEACHER_MODIFY_WORD_STUDY_MODE_SETTING("老师修改认词汇学习模式设置"),
    TEACHER_MODIFY_WORD_INTENSIFY_FLAG_SETTING("老师修改词义强化开关"),
    TEACHER_MODIFY_LOGIN_PASSWORD("老师重置了登录密码"),
    TEACHER_MODIFY_PAY_PASSWORD("老师重置了支付密码"),
    TEACHER_MODIFY_REVIEW_SETTING("老师修改了复习设置"),
    TEACHER_MODIFY_LETTER_STUDY_VERSION_SETTING("老师修改了认字母学习版本"),
    TEACHER_MODIFY_USER_FUNCTION_SETTING("老师修改了辅助功能开关控制"),
    START_REMOTE("开始远程"),
    COURSE_REFRESH("刷新课程"),
    MEDAL_TIPS("勋章弹框提醒"),
    TEACHER_MODIFY_USER_CONFIG("老师修改了个性化设置"),
    ARTICLE_SHARE_TOAST("文章分享toast"),
    PRIVATE_MESSAGE("私聊消息"),
    PRIVATE_MESSAGE_REVOKE("私聊消息撤回"),
    CALL_START("发起通话");

    private final String desc;

    PushActionEnum(String desc) {
        this.desc = desc;
    }
}
