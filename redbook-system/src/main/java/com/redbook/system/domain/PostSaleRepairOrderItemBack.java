package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 寄修单条目备份对象 post_sale_repair_order_item_back
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@ApiModel("寄修单条目备份")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderItemBack implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 寄修单条目id
     */
    @TableField(value = "item_id")
    @Excel(name = "寄修单条目id")
    @ApiModelProperty(name = "itemIdBack", value = "寄修单条目id")
    private Integer itemId;


    /**
     * 设备类型，1:整套（平板+键盘） 2:仅平板 3:仅键盘 4:耳机 5:其他
     */
    @TableField(value = "device_type")
    @Excel(name = "设备类型，1:整套", readConverterExp = "平=板+键盘")
    @ApiModelProperty(name = "deviceTypeBack", value = "设备类型，1:整套")
    private Long deviceType;

    /**
     * 物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:其他
     */
    @TableField(value = "product_types")
    @Excel(name = "物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:其他")
    @ApiModelProperty(name = "productTypesBack", value = "物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:其他")
    private String productTypes;


    /**
     * 物品选择其他时的说明
     */
    @TableField(value = "product_other_text")
    @Excel(name = "物品选择其他时的说明")
    @ApiModelProperty(name = "productOtherTextBack", value = "物品选择其他时的说明")
    private String productOtherText;


    /**
     * 故障描述
     */
    @TableField(value = "fault_desc")
    @Excel(name = "故障描述")
    @ApiModelProperty(name = "faultDescBack", value = "故障描述")
    private String faultDesc;


    /**
     * 型号1
     */
    @TableField(value = "model_id")
    @Excel(name = "型号1")
    @ApiModelProperty(name = "modelIdBack", value = "型号1")
    private Long modelId;


    /**
     * sn码1
     */
    @TableField(value = "product_sn")
    @Excel(name = "sn码1")
    @ApiModelProperty(name = "productSnBack", value = "sn码1")
    private String productSn;


    /**
     * 型号2
     */
    @TableField(value = "model2_id")
    @Excel(name = "型号2")
    @ApiModelProperty(name = "model2IdBack", value = "型号2")
    private String model2Id;


    /**
     * sn码2
     */
    @TableField(value = "product_sn2")
    @Excel(name = "sn码2")
    @ApiModelProperty(name = "productSn2Back", value = "sn码2")
    private String productSn2;

}
