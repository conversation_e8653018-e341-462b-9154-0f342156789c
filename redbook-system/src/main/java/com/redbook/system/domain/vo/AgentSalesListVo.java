package com.redbook.system.domain.vo;

import com.redbook.system.domain.AgentSalesDay;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AgentSalesListVo {
    @ApiModelProperty(name = "name",value= "代理商名称" )
    private String agentName;
    @ApiModelProperty(name = "salesDayList",value= "销售列表" )
    private List<AgentSalesDay>salesDayList;


}
