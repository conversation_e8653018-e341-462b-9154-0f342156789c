package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel(value = "设置配赠的会员课程")
@AllArgsConstructor
@NoArgsConstructor
public class SetAgentGiftAccountDto {
    @ApiModelProperty(name = "id", value = "代理商id")
    private Long id;

    @ApiModelProperty(name = "accountGiftCount1Month", value = "赠送账号数量：1个月")
    private Integer accountGiftCount1Month;

    @ApiModelProperty(name = "accountGiftCount3Month", value = "赠送账号数量：3个月")
    private Integer accountGiftCount3Month;

    @ApiModelProperty(name = "accountGiftCount6Month", value = "赠送账号数量：6个月")
    private Integer accountGiftCount6Month;

    @ApiModelProperty(name = "accountGiftCount12Month", value = "赠送账号数量：12个月")
    private Integer accountGiftCount12Month;
}
