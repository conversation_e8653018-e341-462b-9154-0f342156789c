package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 学生用户对象 user_info
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@ApiModel("学生用户")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("hssword_red_book.user_info")
public class UserInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @Excel(name = "用户id")
    @ApiModelProperty(name = "userId", value = "用户id")
    private String userId;


    /**
     * 密文密码
     */
    @TableField(value = "password")
    @Excel(name = "密文密码")
    @ApiModelProperty(name = "password", value = "密文密码")
    private String password;


    /**
     * 微信号
     */
    @TableField(value = "open_id")
    @Excel(name = "微信号")
    @ApiModelProperty(name = "openId", value = "微信号")
    private String openId;


    /**
     * 手机 绑定了才保存
     */
    @TableField(value = "mobile_phone")
    @Excel(name = "手机 绑定了才保存")
    @ApiModelProperty(name = "mobilePhone", value = "手机 绑定了才保存")
    private String mobilePhone;


    /**
     * 学校id
     */
    @TableField(value = "aid")
    @Excel(name = "学校id")
    @ApiModelProperty(name = "aid", value = "学校id")
    private String aid;

    @TableField(value = "exclusive_shop_id")
    @Excel(name = "专卖店id")
    @ApiModelProperty(name = "exclusiveShopId", value = "专卖店id")
    private Integer exclusiveShopId;

    /**
     * 教师id
     */
    @TableField(value = "teach_id")
    @Excel(name = "教师id")
    @ApiModelProperty(name = "teachId", value = "教师id")
    private String teachId;


    /**
     * 班级id
     */
    @TableField(value = "class_id")
    @Excel(name = "班级id")
    @ApiModelProperty(name = "classId", value = "班级id")
    private Long classId;


    /**
     * 状态表后缀（状态分表使用）
     */
    @TableField(value = "table_suffix")
    @Excel(name = "状态表后缀", readConverterExp = "状=态分表使用")
    @ApiModelProperty(name = "tableSuffix", value = "状态表后缀")
    private String tableSuffix;


    /**
     * 注册日期
     */
    @TableField(value = "register_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "注册日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "registerDate", value = "注册日期")
    private Date registerDate;


    /**
     * 第一次购买会员日期（成为正式会员时间）
     */
    @TableField(value = "first_purchase_date")
    @Excel(name = "第一次购买会员日期", readConverterExp = "成=为正式会员时间")
    @ApiModelProperty(name = "firstPurchaseDate", value = "第一次购买会员日期")
    private Date firstPurchaseDate;


    /**
     * 最后一次购买会员日期
     */
    @TableField(value = "last_purchase_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后一次购买会员日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "lastPurchaseDate", value = "最后一次购买会员日期")
    private Date lastPurchaseDate;


    /**
     * 到期日期
     */
    @TableField(value = "expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expirationDate", value = "到期日期")
    private Date expirationDate;


    /**
     * 会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员
     */
    @TableField(value = "member_type")
    @Excel(name = "会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员  -10：所有体验会员（-1、0） 10：所有正式会员（1、2、3）")
    @ApiModelProperty(name = "memberType", value = "会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员  -10：所有体验会员（-1、0） 10：所有正式会员（1、2、3）")
    private Integer memberType;


    /**
     * 小学学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage1_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage1ExpirationDate", value = "小学学段到期日期，如果为空表示没有购买")
    private Date stage1ExpirationDate;


    /**
     * 初中学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage2_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage2ExpirationDate", value = "初中学段到期日期，如果为空表示没有购买")
    private Date stage2ExpirationDate;


    /**
     * 高中学段到期日期，如果为空表示没有购买
     */
    @TableField(value = "stage3_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "高中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage3ExpirationDate", value = "高中学段到期日期，如果为空表示没有购买")
    private Date stage3ExpirationDate;

    @TableField(value = "stage4_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "大学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage4ExpirationDate", value = "大学学段到期日期，如果为空表示没有购买")
    private Date stage4ExpirationDate;

    @TableField(value = "stage5_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出国学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage5ExpirationDate", value = "出国学段到期日期，如果为空表示没有购买")
    private Date stage5ExpirationDate;

    @TableField(value = "stage11_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小升初学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage11ExpirationDate", value = "小升初学段到期日期，如果为空表示没有购买")
    private Date stage11ExpirationDate;

    @TableField(value = "stage21_expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初升高学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage21ExpirationDate", value = "初升高学段到期日期，如果为空表示没有购买")
    private Date stage21ExpirationDate;


    /**
     * 所属学段（1小学/2初中/3高中）
     */
    @TableField(value = "stage")
    @Excel(name = "所属学段", readConverterExp = "1=小学/2初中/3高中/4大学/5出国/11小升初/21初升高")
    @ApiModelProperty(name = "stage", value = "所属学段")
    private Integer stage;


    /**
     * 主版本ID（必须选择一个,设置好不可修改）
     */
    @TableField(value = "main_version_id")
    @Excel(name = "主版本ID", readConverterExp = "必=须选择一个,设置好不可修改")
    @ApiModelProperty(name = "mainVersionId", value = "主版本ID")
    private Long mainVersionId;


    /**
     * 主课程ID，会随着学完后升级
     */
    @TableField(value = "main_course_id")
    @Excel(name = "主课程ID，会随着学完后升级")
    @ApiModelProperty(name = "mainCourseId", value = "主课程ID，会随着学完后升级")
    private Long mainCourseId;


    /**
     * 学生当前的年级（对应主课程的年级，会随着主课程学完后升级。） 11/12/21/22/31/32/41/42/51/52/61/62(一上、一下..六上、六下) 71/72/81/82/90(七上、七下..九) 100~110(必修一~...)
     */
    @TableField(value = "now_grade")
    @Excel(name = "学生当前的年级", readConverterExp = "对=应主课程的年级，会随着主课程学完后升级。")
    @ApiModelProperty(name = "nowGrade", value = "学生当前的年级")
    private Integer nowGrade;


    /**
     * 首次水平测试得分
     */
    @TableField(value = "first_level_quiz_score")
    @Excel(name = "首次水平测试得分")
    @ApiModelProperty(name = "firstLevelQuizScore", value = "首次水平测试得分")
    private Long firstLevelQuizScore;


    /**
     * 最后一次水平测试得分
     */
    @TableField(value = "last_level_quiz_score")
    @Excel(name = "最后一次水平测试得分")
    @ApiModelProperty(name = "lastLevelQuizScore", value = "最后一次水平测试得分")
    private Long lastLevelQuizScore;


    /**
     * 用户获得的总学分
     */
    @TableField(value = "credits")
    @Excel(name = "用户获得的总学分")
    @ApiModelProperty(name = "credits", value = "用户获得的总学分")
    private Integer credits;


    /**
     * 用户获取的总金币
     */
    @TableField(value = "integral")
    @Excel(name = "用户获取的总金币")
    @ApiModelProperty(name = "integral", value = "用户获取的总金币")
    private Integer integral;


    /**
     * 用户获取的总钻石数
     */
    @TableField(value = "diamonds")
    @Excel(name = "用户获取的总钻石数")
    @ApiModelProperty(name = "diamonds", value = "用户获取的总钻石数")
    private Integer diamonds;


    /**
     * 用户获取的总活跃度
     */
    @TableField(value = "liveness")
    @Excel(name = "用户获取的总活跃度")
    @ApiModelProperty(name = "liveness", value = "用户获取的总活跃度")
    private Integer liveness;


    /**
     * 头像图片路径
     */
    @TableField(value = "head_image_url")
    @Excel(name = "头像图片路径")
    @ApiModelProperty(name = "headImageUrl", value = "头像图片路径")
    private String headImageUrl;


    /**
     * 头像挂件
     */
    @TableField(value = "head_pendant_img")
    @Excel(name = "头像挂件")
    @ApiModelProperty(name = "headPendantImg", value = "头像挂件")
    private String headPendantImg;


    /**
     * 主页皮肤
     */
    @TableField(value = "index_skin_img")
    @Excel(name = "主页皮肤")
    @ApiModelProperty(name = "indexSkinImg", value = "主页皮肤")
    private String indexSkinImg;


    /**
     * 姓名
     */
    @TableField(value = "user_name")
    @Excel(name = "姓名")
    @ApiModelProperty(name = "username", value = "姓名")
    private String username;


    /**
     * 昵称
     */
    @TableField(value = "nick_name")
    @Excel(name = "昵称")
    @ApiModelProperty(name = "nickName", value = "昵称")
    private String nickName;


    /**
     * 性别
     * (0女1男)
     */
    @TableField(value = "sex")
    @Excel(name = "性别 (0女1男)")
    @ApiModelProperty(name = "sex", value = "性别 (0女1男)")
    private Integer sex;


    /**
     * 生日
     */
    @TableField(value = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生日", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "birthday", value = "生日")
    private Date birthday;


    /**
     * 邮箱
     */
    @TableField(value = "email")
    @Excel(name = "邮箱")
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;


    /**
     * qq号码
     */
    @TableField(value = "qq")
    @Excel(name = "qq号码")
    @ApiModelProperty(name = "qq", value = "qq号码")
    private String qq;


    /**
     * 个性签名
     */
    @TableField(value = "signature")
    @Excel(name = "个性签名")
    @ApiModelProperty(name = "signature", value = "个性签名")
    private String signature;


    /**
     * 用户的实际就读学校
     */
    @TableField(value = "school")
    @Excel(name = "用户的实际就读学校")
    @ApiModelProperty(name = "school", value = "用户的实际就读学校")
    private String school;


    /**
     * 学生真实所在的班级
     */
    @TableField(value = "grade_class")
    @Excel(name = "学生真实所在的班级")
    @ApiModelProperty(name = "gradeClass", value = "学生真实所在的班级")
    private String gradeClass;


    /**
     * 学生的地址
     */
    @TableField(value = "address")
    @Excel(name = "学生的地址")
    @ApiModelProperty(name = "address", value = "学生的地址")
    private String address;


    /**
     * 最近一次考试的分数
     */
    @TableField(value = "last_score")
    @Excel(name = "最近一次考试的分数")
    @ApiModelProperty(name = "lastScore", value = "最近一次考试的分数")
    private BigDecimal lastScore;


    /**
     * 支付密码
     */
    @TableField(value = "pay_pwd")
    @Excel(name = "支付密码")
    @ApiModelProperty(name = "payPwd", value = "支付密码")
    private String payPwd;


    /**
     * 累计签到次数
     */
    @TableField(value = "total_sign_count")
    @Excel(name = "累计签到次数")
    @ApiModelProperty(name = "totalSignCount", value = "累计签到次数")
    private Long totalSignCount;

    /**
     * 累计签到次数
     */
    @Getter
    @TableField(value = "is_delete")
    @Excel(name = "是否已过期")
    @ApiModelProperty(name = "isDelete", value = "是否已过期，数据已转移。")
    private Integer isDelete;

    @ApiModelProperty(name = "agentName", value = "代理商名称")
    @TableField(exist = false)
    private String agentName;
    @ApiModelProperty(name = "teacherName", value = "老是名称")
    @TableField(exist = false)
    private String teacherName;
    @ApiModelProperty(name = "className", value = "班级名称")
    @TableField(exist = false)
    private String className;

    @TableField(exist = false)
    private UserOtherInfoBean otherInfoBean;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword() {
        return password;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAid() {
        return aid;
    }

    public void setTeachId(String teachId) {
        this.teachId = teachId;
    }

    public String getTeachId() {
        return teachId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getClassId() {
        return classId;
    }

    public void setTableSuffix(String tableSuffix) {
        this.tableSuffix = tableSuffix;
    }

    public String getTableSuffix() {
        return tableSuffix;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setFirstPurchaseDate(Date firstPurchaseDate) {
        this.firstPurchaseDate = firstPurchaseDate;
    }

    public Date getFirstPurchaseDate() {
        return firstPurchaseDate;
    }

    public void setLastPurchaseDate(Date lastPurchaseDate) {
        this.lastPurchaseDate = lastPurchaseDate;
    }

    public Date getLastPurchaseDate() {
        return lastPurchaseDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setStage1ExpirationDate(Date stage1ExpirationDate) {
        this.stage1ExpirationDate = stage1ExpirationDate;
    }

    public Date getStage1ExpirationDate() {
        return stage1ExpirationDate;
    }

    public void setStage2ExpirationDate(Date stage2ExpirationDate) {
        this.stage2ExpirationDate = stage2ExpirationDate;
    }

    public Date getStage2ExpirationDate() {
        return stage2ExpirationDate;
    }

    public void setStage3ExpirationDate(Date stage3ExpirationDate) {
        this.stage3ExpirationDate = stage3ExpirationDate;
    }

    public Date getStage3ExpirationDate() {
        return stage3ExpirationDate;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getStage() {
        return stage;
    }

    public void setMainVersionId(Long mainVersionId) {
        this.mainVersionId = mainVersionId;
    }

    public Long getMainVersionId() {
        return mainVersionId;
    }

    public void setMainCourseId(Long mainCourseId) {
        this.mainCourseId = mainCourseId;
    }

    public Long getMainCourseId() {
        return mainCourseId;
    }

    public void setNowGrade(Integer nowGrade) {
        this.nowGrade = nowGrade;
    }

    public Integer getNowGrade() {
        return nowGrade;
    }

    public void setFirstLevelQuizScore(Long firstLevelQuizScore) {
        this.firstLevelQuizScore = firstLevelQuizScore;
    }

    public Long getFirstLevelQuizScore() {
        return firstLevelQuizScore;
    }

    public void setLastLevelQuizScore(Long lastLevelQuizScore) {
        this.lastLevelQuizScore = lastLevelQuizScore;
    }

    public Long getLastLevelQuizScore() {
        return lastLevelQuizScore;
    }

    public void setCredits(Integer credits) {
        this.credits = credits;
    }

    public Integer getCredits() {
        return credits;
    }

    public void setIntegral(Integer integral) {
        this.integral = integral;
    }

    public Integer getIntegral() {
        return integral;
    }

    public void setDiamonds(Integer diamonds) {
        this.diamonds = diamonds;
    }

    public Integer getDiamonds() {
        return diamonds;
    }

    public void setLiveness(Integer liveness) {
        this.liveness = liveness;
    }

    public Integer getLiveness() {
        return liveness;
    }

    public void setHeadImageUrl(String headImageUrl) {
        this.headImageUrl = headImageUrl;
    }

    public String getHeadImageUrl() {
        return headImageUrl;
    }

    public void setHeadPendantImg(String headPendantImg) {
        this.headPendantImg = headPendantImg;
    }

    public String getHeadPendantImg() {
        return headPendantImg;
    }

    public void setIndexSkinImg(String indexSkinImg) {
        this.indexSkinImg = indexSkinImg;
    }

    public String getIndexSkinImg() {
        return indexSkinImg;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getSex() {
        return sex;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getQq() {
        return qq;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getSignature() {
        return signature;
    }

    public void setSchool(String school) {
        this.school = school;
    }

    public String getSchool() {
        return school;
    }

    public void setGradeClass(String gradeClass) {
        this.gradeClass = gradeClass;
    }

    public String getGradeClass() {
        return gradeClass;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setLastScore(BigDecimal lastScore) {
        this.lastScore = lastScore;
    }

    public BigDecimal getLastScore() {
        return lastScore;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setTotalSignCount(Long totalSignCount) {
        this.totalSignCount = totalSignCount;
    }

    public Long getTotalSignCount() {
        return totalSignCount;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public Date getStage4ExpirationDate() {
        return stage4ExpirationDate;
    }

    public void setStage4ExpirationDate(Date stage4ExpirationDate) {
        this.stage4ExpirationDate = stage4ExpirationDate;
    }

    public Date getStage5ExpirationDate() {
        return stage5ExpirationDate;
    }

    public void setStage5ExpirationDate(Date stage5ExpirationDate) {
        this.stage5ExpirationDate = stage5ExpirationDate;
    }

    public Date getStage11ExpirationDate() {
        return stage11ExpirationDate;
    }

    public void setStage11ExpirationDate(Date stage11ExpirationDate) {
        this.stage11ExpirationDate = stage11ExpirationDate;
    }

    public Date getStage21ExpirationDate() {
        return stage21ExpirationDate;
    }

    public void setStage21ExpirationDate(Date stage21ExpirationDate) {
        this.stage21ExpirationDate = stage21ExpirationDate;
    }

    public Integer getExclusiveShopId() {
        return exclusiveShopId;
    }

    public void setExclusiveShopId(Integer exclusiveShopId) {
        this.exclusiveShopId = exclusiveShopId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("password", getPassword())
                .append("openId", getOpenId())
                .append("mobilePhone", getMobilePhone())
                .append("aid", getAid())
                .append("teachId", getTeachId())
                .append("classId", getClassId())
                .append("tableSuffix", getTableSuffix())
                .append("registerDate", getRegisterDate())
                .append("firstPurchaseDate", getFirstPurchaseDate())
                .append("lastPurchaseDate", getLastPurchaseDate())
                .append("expirationDate", getExpirationDate())
                .append("memberType", getMemberType())
                .append("stage1ExpirationDate", getStage1ExpirationDate())
                .append("stage2ExpirationDate", getStage2ExpirationDate())
                .append("stage3ExpirationDate", getStage3ExpirationDate())
                .append("stage", getStage())
                .append("mainVersionId", getMainVersionId())
                .append("mainCourseId", getMainCourseId())
                .append("nowGrade", getNowGrade())
                .append("firstLevelQuizScore", getFirstLevelQuizScore())
                .append("lastLevelQuizScore", getLastLevelQuizScore())
                .append("credits", getCredits())
                .append("integral", getIntegral())
                .append("diamonds", getDiamonds())
                .append("liveness", getLiveness())
                .append("headImageUrl", getHeadImageUrl())
                .append("headPendantImg", getHeadPendantImg())
                .append("indexSkinImg", getIndexSkinImg())
                .append("userName", getUsername())
                .append("nickName", getNickName())
                .append("sex", getSex())
                .append("birthday", getBirthday())
                .append("email", getEmail())
                .append("qq", getQq())
                .append("signature", getSignature())
                .append("school", getSchool())
                .append("gradeClass", getGradeClass())
                .append("address", getAddress())
                .append("lastScore", getLastScore())
                .append("payPwd", getPayPwd())
                .append("totalSignCount", getTotalSignCount())
                .append("isDelete", getIsDelete())
                .toString();
    }
}
