package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 商品尺码库存对象 product_size_inventory
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
@ApiModel("商品尺码库存")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductSizeInventory extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 商品尺码库存ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /** 商品ID */
    @TableField(value = "product_id")
    @Excel(name = "商品ID")
    @ApiModelProperty(name = "productId",value= "商品ID" )
    private Integer productId;


    /** 商品尺码 */
    @TableField(value = "size")
    @Excel(name = "商品尺码")
    @ApiModelProperty(name = "size",value= "商品尺码" )
    private String size;


    /** 商品尺码对应的库存 */
    @TableField(value = "inventory")
    @Excel(name = "商品尺码对应的库存")
    @ApiModelProperty(name = "inventory",value= "商品尺码对应的库存" )
    private Integer inventory;

    @TableField(exist = false)
    @ApiModelProperty(name = "orderCount",value= "订购数量，下订单使用" )
    private Integer orderCount;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setProductId(Integer productId) 
    {
        this.productId = productId;
    }

    public Integer getProductId() 
    {
        return productId;
    }
    public void setSize(String size) 
    {
        this.size = size;
    }

    public String getSize() 
    {
        return size;
    }
    public void setInventory(Integer inventory) 
    {
        this.inventory = inventory;
    }

    public Integer getInventory() 
    {
        return inventory;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("size", getSize())
            .append("inventory", getInventory())
            .toString();
    }
}
