package com.redbook.system.domain.dto;

import com.redbook.system.domain.vo.ExclusiveShopMemberPricePackageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("专卖店会员批量价格保存")
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExclusiveShopBatchSaveMemberPriceDto {
    @NotNull(message = "专卖店id列表不能为空")
    @ApiModelProperty(value = "专卖店id列表")
    private List<Integer> exclusiveShopIds;
    @ApiModelProperty(value = "会员价格列表")
    List<ExclusiveShopMemberPricePackageVo> priceList;
    @NotNull
    @ApiModelProperty(value = "是否支持续费赠送 false不支持 true支持")
    Boolean renewPresentSupport;
}
