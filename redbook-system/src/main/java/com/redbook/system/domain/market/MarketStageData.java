package com.redbook.system.domain.market;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-19 15:43
 */
public class MarketStageData implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "小学")
    private String stageName;
    @ApiModelProperty(value = "阶段数字： 1 小学  2 初中  3 高中")
    private Integer stageNum;

    @ApiModelProperty(value = "版本集合")
    private List<MarketVersionData> versionDataList;

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public Integer getStageNum() {
        return stageNum;
    }

    public void setStageNum(Integer stageNum) {
        this.stageNum = stageNum;
    }

    public List<MarketVersionData> getVersionDataList() {
        return versionDataList;
    }

    public void setVersionDataList(List<MarketVersionData> versionDataList) {
        this.versionDataList = versionDataList;
    }
}
