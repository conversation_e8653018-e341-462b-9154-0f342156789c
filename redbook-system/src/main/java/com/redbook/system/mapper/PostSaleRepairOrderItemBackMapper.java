package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleRepairOrderItemBack;

/**
 * 寄修单条目备份Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface PostSaleRepairOrderItemBackMapper extends BaseMapper<PostSaleRepairOrderItemBack> {
    /**
     * 新增寄修单条目备份
     *
     * @param postSaleRepairOrderItemBack 寄修单条目备份
     * @return 结果
     */
    int insertPostSaleRepairOrderItemBack(PostSaleRepairOrderItemBack postSaleRepairOrderItemBack);

    /**
     * 修改寄修单条目备份
     *
     * @param postSaleRepairOrderItemBack 寄修单条目备份
     * @return 结果
     */
    int updatePostSaleRepairOrderItemBack(PostSaleRepairOrderItemBack postSaleRepairOrderItemBack);


}
