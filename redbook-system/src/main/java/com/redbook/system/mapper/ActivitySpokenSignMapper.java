package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.ActivityDrawRecord;
import com.redbook.system.domain.ActivitySpokenSignUser;
import com.redbook.system.domain.dto.ActivitySpokenSignIndexListDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivitySpokenSignMapper extends BaseMapper<ActivitySpokenSignUser> {


    List<ActivitySpokenSignUser> selectUserList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto);
    List<ActivitySpokenSignUser> selectEnter100UserList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto);
    List<ActivitySpokenSignUser> selectEnter10UserList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto);

    List<ActivityDrawRecord> selectUserDrawRecordList(@Param("activityBaseId") Integer activityBaseId, @Param("userId") String userId);

    List<ActivitySpokenSignUser> selectTop100ByStage(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage);

    List<ActivitySpokenSignUser> selectTop10ByStage(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage);

    List<ActivitySpokenSignUser> selectTopFinalRank(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage);
}
