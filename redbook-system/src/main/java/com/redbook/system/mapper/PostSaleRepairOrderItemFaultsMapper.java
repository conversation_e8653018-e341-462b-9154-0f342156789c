package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleRepairOrderItemFaults;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 寄修单故障Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleRepairOrderItemFaultsMapper extends BaseMapper<PostSaleRepairOrderItemFaults> {

    void insertBatch(@Param("list") List<PostSaleRepairOrderItemFaults> list);

    //查询寄修单条目的故障分类
    List<PostSaleRepairOrderItemFaults> selectPostSaleRepairOrderItemFaultsByItemIds(@Param("list") List<Integer> list);
    List<PostSaleRepairOrderItemFaults> selectPostSaleRepairOrderItemFaultsByItemId(@Param("itemId") Integer itemId);

    //查询故障名称
    List<String> selectPostSaleRepairOrderItemFaultsNamesByItemId(@Param("itemId") Integer itemId);

    void deleteBatch(@Param("itemId") Integer itemId, @Param("list") List<Integer> list);
}
