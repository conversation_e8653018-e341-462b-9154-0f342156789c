package com.redbook.system.mapper;

import java.util.List;
import com.redbook.system.domain.PostSaleRepairOrderItemFeeSparePart;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 寄修单条目费用维修物料对应Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface PostSaleRepairOrderItemFeeSparePartMapper extends BaseMapper<PostSaleRepairOrderItemFeeSparePart>
{


    /**
     * 新增寄修单条目费用维修物料对应
     * 
     * @param postSaleRepairOrderItemFeeSparePart 寄修单条目费用维修物料对应
     * @return 结果
     */
    int insertPostSaleRepairOrderItemFeeSparePart(PostSaleRepairOrderItemFeeSparePart postSaleRepairOrderItemFeeSparePart);

    /**
     * 修改寄修单条目费用维修物料对应
     * 
     * @param postSaleRepairOrderItemFeeSparePart 寄修单条目费用维修物料对应
     * @return 结果
     */
    int updatePostSaleRepairOrderItemFeeSparePart(PostSaleRepairOrderItemFeeSparePart postSaleRepairOrderItemFeeSparePart);


    List<PostSaleRepairOrderItemFeeSparePart> selectPostSaleRepairOrderItemFeeSparePartByItemId(@Param("itemId") Integer itemId);

    void insertBatch(@Param("list") List<PostSaleRepairOrderItemFeeSparePart> list);

    List<PostSaleRepairOrderItemFeeSparePart> selectFeeSpartPartByFeeIds(@Param("list") List<Integer> list);
}
