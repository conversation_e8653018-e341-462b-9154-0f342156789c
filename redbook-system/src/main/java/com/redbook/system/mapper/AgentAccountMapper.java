package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.AgentAccount;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理商钱包账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
public interface AgentAccountMapper extends BaseMapper<AgentAccount>
{
    /**
     * 查询代理商钱包账户
     * 
     * @param id 代理商钱包账户主键
     * @return 代理商钱包账户
     */
    AgentAccount selectAgentAccountById(Long id);

    /**
     * 查询代理商钱包账户列表
     * 
     * @param agentAccount 代理商钱包账户
     * @return 代理商钱包账户集合
     */
    List<AgentAccount> selectAgentAccountList(AgentAccount agentAccount);

    /**
     * 新增代理商钱包账户
     * 
     * @param agentAccount 代理商钱包账户
     * @return 结果
     */
    int insertAgentAccount(AgentAccount agentAccount);

    /**
     * 修改代理商钱包账户
     * 
     * @param agentAccount 代理商钱包账户
     * @return 结果
     */
    int updateAgentAccount(AgentAccount agentAccount);

    /**
     * 删除代理商钱包账户
     * 
     * @param id 代理商钱包账户主键
     * @return 结果
     */
    int deleteAgentAccountById(Long id);

    /**
     * 批量删除代理商钱包账户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentAccountByIds(Long[] ids);

    AgentAccount getByAgentId(long agentId);

    /**
     * 代理商钱包账户查询
     * @param agentId 代理商id
     * @param fundType 资金类型
     * @param paymentType  进出类型 1消费 0充值
     * @return
     */
    BigDecimal getMoney(@Param("agentId") Long agentId, @Param("fundType") Integer fundType, @Param("paymentType") Integer paymentType);


    int changePayPassword(@Param("userId") Long userId, @Param("payPassword") String payPassword);

    boolean openRebate(Long agentId);
}
