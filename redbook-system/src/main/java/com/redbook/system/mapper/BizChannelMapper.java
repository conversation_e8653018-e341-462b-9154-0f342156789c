package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.BizChannel;

import java.util.List;
/**
 * 渠道Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface BizChannelMapper extends BaseMapper<BizChannel>
{
    /**
     * 查询渠道
     * 
     * @param channelId 渠道主键
     * @return 渠道
     */
        BizChannel selectBizChannelByChannelId(Long channelId);

    /**
     * 查询渠道列表
     * 
     * @param bizChannel 渠道
     * @return 渠道集合
     */
    List<BizChannel> selectBizChannelList(BizChannel bizChannel);

    /**
     * 新增渠道
     * 
     * @param bizChannel 渠道
     * @return 结果
     */
    int insertBizChannel(BizChannel bizChannel);

    /**
     * 修改渠道
     * 
     * @param bizChannel 渠道
     * @return 结果
     */
    int updateBizChannel(BizChannel bizChannel);

    /**
     * 删除渠道
     * 
     * @param channelId 渠道主键
     * @return 结果
     */
    int deleteBizChannelByChannelId(Long channelId);

    /**
     * 批量删除渠道
     * 
     * @param channelIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBizChannelByChannelIds(Long[] channelIds);
}
