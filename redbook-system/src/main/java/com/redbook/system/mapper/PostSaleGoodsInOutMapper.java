package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleGoodsInOut;

import java.util.List;
/**
 * 商品出入库Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleGoodsInOutMapper extends BaseMapper<PostSaleGoodsInOut>
{
    /**
     * 查询商品出入库
     * 
     * @param id 商品出入库主键
     * @return 商品出入库
     */
        PostSaleGoodsInOut selectPostSaleGoodsInOutById(Long id);

    /**
     * 查询商品出入库列表
     * 
     * @param postSaleGoodsInOut 商品出入库
     * @return 商品出入库集合
     */
    List<PostSaleGoodsInOut> selectPostSaleGoodsInOutList(PostSaleGoodsInOut postSaleGoodsInOut);

    /**
     * 新增商品出入库
     * 
     * @param postSaleGoodsInOut 商品出入库
     * @return 结果
     */
    int insertPostSaleGoodsInOut(PostSaleGoodsInOut postSaleGoodsInOut);

    /**
     * 修改商品出入库
     * 
     * @param postSaleGoodsInOut 商品出入库
     * @return 结果
     */
    int updatePostSaleGoodsInOut(PostSaleGoodsInOut postSaleGoodsInOut);

    /**
     * 删除商品出入库
     * 
     * @param id 商品出入库主键
     * @return 结果
     */
    int deletePostSaleGoodsInOutById(Long id);

    /**
     * 批量删除商品出入库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleGoodsInOutByIds(Long[] ids);
}
