package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleSparePartInOut;
import com.redbook.system.domain.PostSaleSummaryCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备件出入库Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleSparePartInOutMapper extends BaseMapper<PostSaleSparePartInOut>
{
    /**
     * 查询备件出入库
     * 
     * @param id 备件出入库主键
     * @return 备件出入库
     */
        PostSaleSparePartInOut selectPostSaleSparePartInOutById(Long id);
    PostSaleSummaryCount summaryCount();
    /**
     * 查询备件出入库列表
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 备件出入库集合
     */
    List<PostSaleSparePartInOut> selectPostSaleSparePartInOutList(PostSaleSparePartInOut postSaleSparePartInOut);

    /**
     * 新增备件出入库
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 结果
     */
    int insertPostSaleSparePartInOut(PostSaleSparePartInOut postSaleSparePartInOut);

    /**
     * 修改备件出入库
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 结果
     */
    int updatePostSaleSparePartInOut(PostSaleSparePartInOut postSaleSparePartInOut);

    /**
     * 删除备件出入库
     * 
     * @param id 备件出入库主键
     * @return 结果
     */
    int deletePostSaleSparePartInOutById(Long id);

    /**
     * 批量删除备件出入库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleSparePartInOutByIds(Long[] ids);

    void insertBatch(@Param("list") List<PostSaleSparePartInOut> list);

}
