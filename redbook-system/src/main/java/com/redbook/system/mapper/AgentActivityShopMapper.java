package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.AgentActivityShop;

import java.util.List;
/**
 * 代理商活动-专卖店Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface AgentActivityShopMapper extends BaseMapper<AgentActivityShop>
{
    /**
     * 查询代理商活动-专卖店
     * 
     * @param id 代理商活动-专卖店主键
     * @return 代理商活动-专卖店
     */
        AgentActivityShop selectAgentActivityShopById(Long id);

    List<AgentActivityShop> shopList(Integer activityId);
    /**
     * 查询代理商活动-专卖店列表
     * 
     * @param agentActivityShop 代理商活动-专卖店
     * @return 代理商活动-专卖店集合
     */
    List<AgentActivityShop> selectAgentActivityShopList(AgentActivityShop agentActivityShop);

    /**
     * 新增代理商活动-专卖店
     * 
     * @param agentActivityShop 代理商活动-专卖店
     * @return 结果
     */
    int insertAgentActivityShop(AgentActivityShop agentActivityShop);

    /**
     * 修改代理商活动-专卖店
     * 
     * @param agentActivityShop 代理商活动-专卖店
     * @return 结果
     */
    int updateAgentActivityShop(AgentActivityShop agentActivityShop);

    /**
     * 删除代理商活动-专卖店
     * 
     * @param id 代理商活动-专卖店主键
     * @return 结果
     */
    int deleteAgentActivityShopById(Long id);
    int deleteAgentActivityShopByActivityId(Integer activityId);

    /**
     * 批量删除代理商活动-专卖店
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentActivityShopByIds(Long[] ids);
}
