<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleGoodsOrderDetailMapper">
    
    <resultMap type="PostSaleGoodsOrderDetail" id="PostSaleGoodsOrderDetailResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="goodId"    column="good_id"    />
        <result property="goodName"    column="good_name"    />
        <result property="goodNum"    column="good_num"    />
        <result property="goodPrice"    column="good_price"    />
        <result property="totalMoney"    column="total_money"    />
    </resultMap>

    <sql id="selectPostSaleGoodsOrderDetailVo">
        select id, order_id, good_id,good_name, good_num, total_money from post_sale_goods_order_detail
    </sql>

    <select id="selectPostSaleGoodsOrderDetailList" parameterType="PostSaleGoodsOrderDetail" resultMap="PostSaleGoodsOrderDetailResult">
        <include refid="selectPostSaleGoodsOrderDetailVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="goodId != null "> and good_id = #{goodId}</if>
            <if test="goodNum != null "> and good_num = #{goodNum}</if>
            <if test="totalMoney != null "> and total_money = #{totalMoney}</if>
        </where>
    </select>

    <select id="selectByOrderId" resultMap="PostSaleGoodsOrderDetailResult">
        select d.id, d.order_id, d.good_id,d.good_name, d.good_num, d.total_money,p.price as good_price
        from post_sale_goods_order_detail d
        left join product p on p.id = d.good_id
        where d.order_id = #{orderId}
    </select>

    <select id="selectPostSaleGoodsOrderDetailById" parameterType="Long" resultMap="PostSaleGoodsOrderDetailResult">
        <include refid="selectPostSaleGoodsOrderDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleGoodsOrderDetail" parameterType="PostSaleGoodsOrderDetail" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_goods_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="goodId != null">good_id,</if>
            <if test="goodName != null">good_name,</if>
            <if test="goodNum != null">good_num,</if>
            <if test="totalMoney != null">total_money,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="goodId != null">#{goodId},</if>
            <if test="goodName != null">#{goodName},</if>
            <if test="goodNum != null">#{goodNum},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
         </trim>
    </insert>

    <insert id="batchInsertGoodsOrderDetail">
        insert into post_sale_goods_order_detail (order_id, good_id,good_name, good_num, total_money)
        values
        <foreach item="item" collection="list" separator=",">
            (
                #{item.orderId},
                #{item.goodId},
                #{item.goodName},
                #{item.goodNum},
                #{item.totalMoney}
            )
        </foreach>
    </insert>

    <update id="updatePostSaleGoodsOrderDetail" parameterType="PostSaleGoodsOrderDetail">
        update post_sale_goods_order_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="goodId != null">good_id = #{goodId},</if>
            <if test="goodName != null">good_name = #{goodName},</if>
            <if test="goodNum != null">good_num = #{goodNum},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleGoodsOrderDetailById" parameterType="Long">
        delete from post_sale_goods_order_detail where id = #{id}
    </delete>

    <delete id="deletePostSaleGoodsOrderDetailByIds" parameterType="String">
        delete from post_sale_goods_order_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>