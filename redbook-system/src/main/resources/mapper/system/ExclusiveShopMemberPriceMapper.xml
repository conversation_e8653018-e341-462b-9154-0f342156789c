<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ExclusiveShopMemberPriceMapper">
    
    <resultMap type="ExclusiveShopMemberPrice" id="ExclusiveShopMemberPriceResult">
        <result property="id"    column="id"    />
        <result property="exclusiveShopId"    column="exclusive_shop_id"    />
        <result property="stage"    column="stage"    />
        <result property="timeLen"    column="time_len"    />
        <result property="price"    column="price"    />
    </resultMap>

    <sql id="selectExclusiveShopMemberPriceVo">
        select id, exclusive_shop_id, stage, time_len, price from exclusive_shop_member_price
    </sql>

    <select id="selectExclusiveShopMemberPriceList" parameterType="ExclusiveShopMemberPrice" resultMap="ExclusiveShopMemberPriceResult">
        <include refid="selectExclusiveShopMemberPriceVo"/>
        <where>  
            <if test="exclusiveShopId != null "> and exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="stage != null "> and stage = #{stage}</if>
            <if test="timeLen != null "> and time_len = #{timeLen}</if>
            <if test="price != null "> and price = #{price}</if>
        </where>
        order by stage
    </select>
    
    <select id="selectExclusiveShopMemberPrice" resultMap="ExclusiveShopMemberPriceResult">
        <include refid="selectExclusiveShopMemberPriceVo"/>
        where exclusive_shop_id = #{exclusiveShopId} and stage = #{stage} and time_len = #{timeLen}
    </select>
        
    <insert id="insertExclusiveShopMemberPrice" parameterType="ExclusiveShopMemberPrice" useGeneratedKeys="true" keyProperty="id">
        insert into exclusive_shop_member_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exclusiveShopId != null">exclusive_shop_id,</if>
            <if test="stage != null">stage,</if>
            <if test="timeLen != null">time_len,</if>
            <if test="price != null">price,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="exclusiveShopId != null">#{exclusiveShopId},</if>
            <if test="stage != null">#{stage},</if>
            <if test="timeLen != null">#{timeLen},</if>
            <if test="price != null">#{price},</if>
         </trim>
    </insert>

    <update id="updateExclusiveShopMemberPrice" parameterType="ExclusiveShopMemberPrice">
        update exclusive_shop_member_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="exclusiveShopId != null">exclusive_shop_id = #{exclusiveShopId},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="timeLen != null">time_len = #{timeLen},</if>
            <if test="price != null">price = #{price},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExclusiveShopMemberPriceById" parameterType="Long">
        delete from exclusive_shop_member_price where id = #{id}
    </delete>

    <delete id="deleteExclusiveShopMemberPriceByIds" parameterType="String">
        delete from exclusive_shop_member_price where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>