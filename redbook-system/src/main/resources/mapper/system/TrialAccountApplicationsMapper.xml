<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.TrialAccountApplicationsMapper">

    <resultMap type="TrialAccountApplications" id="TrialAccountApplicationsResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="applicantId"    column="applicant_id"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="status"    column="status"    />
        <result property="quantity"    column="quantity"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="reason"    column="reason"    />
        <result property="shopName"    column="shop_name"    />
        <result property="agentName"    column="agent_name"    />
        <result property="applicantUserName"    column="applicant_user_name"    />
        <result property="operatorName"    column="operator_name"    />

    </resultMap>

  <!--  {
    private static final long serialVersionUID = 1L;


    /** 申请记录ID，自增主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 专卖店ID */
    @TableField(value = "shop_id")
    @Excel(name = "专卖店ID")
    @ApiModelProperty(name = "shopId",value= "专卖店ID" )
    private Long shopId;


    /** 代理商ID */
    @TableField(value = "agent_id")
    @Excel(name = "代理商ID")
    @ApiModelProperty(name = "agentId",value= "代理商ID" )
    private Long agentId;


    /** 发起人ID */
    @TableField(value = "applicant_id")
    @Excel(name = "发起人ID")
    @ApiModelProperty(name = "applicantId",value= "发起人ID" )
    private Long applicantId;

    @TableField(exist = false)
    @Excel(name = "专卖店名称")
    @ApiModelProperty(name = "shopName",value= "专卖店名称" )
    private String shopName;

    @TableField(exist = false)
    @Excel(name = "代理商名称")
    @ApiModelProperty(name = "agentName",value= "代理商名称" )
    private String agentName;

    @TableField(exist = false)
    @Excel(name = "发起人名称")
    @ApiModelProperty(name = "applicantUserName",value= "发起人名称" )
    private String applicantUserName;

    /** 发起时间，默认为当前时间戳 */
    @TableField(value = "application_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发起时间，默认为当前时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "applicationTime",value= "发起时间，默认为当前时间戳" )
    private Date applicationTime;


    /** 申请状态：pending(待审核)，approved(已批准)，rejected(已拒绝)，cancelled(已取消) */
    @TableField(value = "status")
    @Excel(name = "申请状态：pending(待审核)，approved(已批准)，rejected(已拒绝)，cancelled(已取消)")
    @ApiModelProperty(name = "status",value= "申请状态：pending(待审核)，approved(已批准)，rejected(已拒绝)，cancelled(已取消)" )
    private String status;


    /** 申请数量，默认为1 */
    @TableField(value = "quantity")
    @Excel(name = "申请数量，默认为1")
    @ApiModelProperty(name = "quantity",value= "申请数量，默认为1" )
    private Long quantity;


    /** 操作人ID（代理商） */
    @TableField(value = "operator_id")
    @Excel(name = "操作人ID", readConverterExp = "代理商")
    @ApiModelProperty(name = "operatorId",value= "操作人ID" )
    private Long operatorId;

    @TableField(exist = false)
    @Excel(name = "操作人名称", readConverterExp = "代理商")
    @ApiModelProperty(name = "operatorName",value= "操作人名称" )
    private String operatorName;


    /** 操作时间 */
    @TableField(value = "operation_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:SS")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "operationTime",value= "操作时间" )
    private Date operationTime;


    /** 备注/原因，用于记录拒绝原因或特殊说明 */
    @TableField(value = "reason")
    @Excel(name = "备注/原因，用于记录拒绝原因或特殊说明")
    @ApiModelProperty(name = "reason",value= "备注/原因，用于记录拒绝原因或特殊说明" )
    private String reason;



  -->

    <sql id="selectTrialAccountApplicationsVo">
        SELECT
            taa.id,
            taa.shop_id,
            es.name AS shop_name,
            taa.agent_id,
            a.name AS agent_name,
            taa.applicant_id,
            su_applicant.nick_name AS applicant_user_name,
            taa.application_time,
            taa.status,
            taa.quantity,
            taa.operator_id,
            su_operator.nick_name AS operator_name,
            taa.operation_time,
            taa.reason
        FROM
            trial_account_applications taa
                LEFT JOIN
            exclusive_shop es ON taa.shop_id = es.id
                LEFT JOIN
            agent a ON taa.agent_id = a.id
                LEFT JOIN
            sys_user su_applicant ON taa.applicant_id = su_applicant.user_id
                LEFT JOIN
            sys_user su_operator ON taa.operator_id = su_operator.user_id
    </sql>

    <select id="selectTrialAccountApplicationsList" parameterType="TrialAccountApplications" resultMap="TrialAccountApplicationsResult">
        <include refid="selectTrialAccountApplicationsVo"/>
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>  
            <if test="shopId != null "> and taa.shop_id = #{shopId}</if>
            <if test="applicantId != null "> and taa.applicant_id = #{applicantId}</if>
            <if test="applicationTime != null "> and taa.application_time = #{applicationTime}</if>
            <if test="status != null  and status != ''"> and taa.status = #{status}</if>
            <if test="quantity != null "> and taa.quantity = #{quantity}</if>
            <if test="operatorId != null "> and taa.operator_id = #{operatorId}</if>
            <if test="operationTime != null "> and taa.operation_time = #{operationTime}</if>
            <if test="reason != null  and reason != ''"> and taa.reason = #{reason}</if>
            <if test="agentId != null">
                and a.id=#{agentId}
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by taa.id desc
    </select>
    
    <select id="selectTrialAccountApplicationsById" parameterType="Long" resultMap="TrialAccountApplicationsResult">
        <include refid="selectTrialAccountApplicationsVo"/>
        where taa.id = #{id}
    </select>
        
    <insert id="insertTrialAccountApplications" parameterType="TrialAccountApplications" useGeneratedKeys="true" keyProperty="id">
        insert into trial_account_applications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="applicantId != null">applicant_id,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="status != null">status,</if>
            <if test="quantity != null">quantity,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="reason != null">reason,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="applicantId != null">#{applicantId},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="status != null">#{status},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="reason != null">#{reason},</if>
         </trim>
    </insert>

    <update id="updateTrialAccountApplications" parameterType="TrialAccountApplications">
        update trial_account_applications
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="applicantId != null">applicant_id = #{applicantId},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="reason != null">reason = #{reason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTrialAccountApplicationsById" parameterType="Long">
        delete from trial_account_applications where id = #{id}
    </delete>

    <delete id="deleteTrialAccountApplicationsByIds" parameterType="String">
        delete from trial_account_applications where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>