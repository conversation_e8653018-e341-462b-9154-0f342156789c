<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSalePayOrderMapper">
    
    <resultMap type="PostSalePayOrder" id="PostSalePayOrderResult">
        <result property="id"    column="id"    />
        <result property="bizType"    column="biz_type"    />
        <result property="bizOrderId"    column="biz_order_id"    />
        <result property="bizOrderNo"    column="biz_order_no"    />
        <result property="amount"    column="amount"    />
        <result property="status"    column="status"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="payTime"    column="pay_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectPostSalePayOrderVo">
        select id, biz_type, biz_order_id,biz_order_no, amount, status, transaction_id, create_time, update_time,pay_time,create_by from post_sale_pay_order
    </sql>

    <select id="selectPostSalePayOrderList" parameterType="PostSalePayOrder" resultMap="PostSalePayOrderResult">
        <include refid="selectPostSalePayOrderVo"/>
        <where>  
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="bizOrderNo != null  and bizOrderNo != ''"> and biz_order_no = #{bizOrderNo}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
        </where>
    </select>
    <select id="listByCreateUser" parameterType="PostSalePayOrder" resultMap="PostSalePayOrderResult">
        <include refid="selectPostSalePayOrderVo"/>
        where status = 0 and create_by = #{createBy}
    </select>
    
    <select id="selectPostSalePayOrderById" resultMap="PostSalePayOrderResult">
        <include refid="selectPostSalePayOrderVo"/>
        where id = #{id}
    </select>
    <select id="selectByBizOrderNo" resultMap="PostSalePayOrderResult">
        <include refid="selectPostSalePayOrderVo"/>
        where biz_order_no = #{bizOrderNo}
    </select>
        
    <insert id="insertPostSalePayOrder" parameterType="PostSalePayOrder" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_pay_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">biz_type,</if>
            <if test="bizOrderId != null and bizOrderId != ''">biz_order_id,</if>
            <if test="bizOrderNo != null and bizOrderNo != ''">biz_order_no,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="payTime != null">pay_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">#{bizType},</if>
            <if test="bizOrderId != null and bizOrderId != ''">#{bizOrderId},</if>
            <if test="bizOrderNo != null and bizOrderNo != ''">#{bizOrderNo},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="payTime != null">#{payTime},</if>
         </trim>
    </insert>

    <update id="updatePostSalePayOrder" parameterType="PostSalePayOrder">
        update post_sale_pay_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">biz_type = #{bizType},</if>
            <if test="bizOrderId != null and bizOrderId != ''">biz_order_id = #{bizOrderId},</if>
            <if test="bizOrderNo != null and bizOrderNo != ''">biz_order_no = #{bizOrderNo},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="closeOrder">
        update post_sale_pay_order
        set status = 3,update_time = now()
        where biz_order_no = #{bizOrderNo}
    </update>


    <delete id="deletePostSalePayOrderById" parameterType="Long">
        delete from post_sale_pay_order where id = #{id}
    </delete>

    <delete id="deletePostSalePayOrderByIds" parameterType="String">
        delete from post_sale_pay_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>