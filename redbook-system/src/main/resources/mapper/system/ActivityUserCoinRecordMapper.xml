<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityUserCoinRecordMapper">

    <resultMap type="ActivityUserCoinRecord" id="ActivityUserCoinRecordResult">
        <result property="id" column="id"/>
        <result property="activityBaseId" column="activity_base_id"/>
        <result property="activityContentId" column="activity_content_id"/>
        <result property="userId" column="user_id"/>
        <result property="aid" column="aid"/>
        <result property="aname" column="aname"/>
        <result property="changeNum" column="change_num"/>
        <result property="changeType" column="change_type"/>
        <result property="createTime" column="create_time"/>
        <result property="coinType" column="coin_type"/>
        <result property="exchangeStatus" column="exchange_status"/>
        <result property="exchangeNo" column="exchange_no"/>
        <result property="exchangeTime" column="exchange_time"/>
        <result property="exchangeStage" column="exchange_stage"/>
        <result property="exchangeExpirationDateBefore" column="exchange_expiration_date_before"/>
        <result property="exchangeExpirationDateAfter" column="exchange_expiration_date_after"/>
        <result property="extra" column="extra"/>
        <result property="userName" column="user_name"/>
    </resultMap>

    <sql id="selectActivityUserCoinRecordVo">
        select aucr.id, aucr.activity_base_id, aucr.activity_content_id, aucr.user_id, aucr.aid, aucr.aname, aucr.change_num,
         aucr.change_type, aucr.create_time, aucr.coin_type, aucr.exchange_status, aucr.exchange_no,
         aucr.exchange_time, aucr.exchange_stage, aucr.exchange_expiration_date_before,
          aucr.exchange_expiration_date_after, aucr.extra,u1.user_name
         FROM ${DB_RED_BOOK_ACTIVITY}.activity_user_coin_record aucr
         left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=aucr.user_id
         left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
         left join sys_user u2 ON a.contact_person = u2.user_id
    </sql>

    <select id="selectActivityUserCoinRecordList" parameterType="ActivityUserCoinRecord"
            resultMap="ActivityUserCoinRecordResult">
        <include refid="selectActivityUserCoinRecordVo"/>
        <where>
            <if test="activityBaseId != null ">and activity_base_id = #{activityBaseId}</if>
            <if test="activityContentId != null ">and activity_content_id = #{activityContentId}</if>
            <if test="userId != null  and userId != ''"> and aucr.user_id = #{userId}</if>
            <if test="agentId != null"> and a.id = #{agentId} </if>
            <if test="aid != null  and aid != ''">and aid = #{aid}</if>
            <if test="aname != null  and aname != ''">and aucr.aname like concat('%', #{aname}, '%')</if>
            <if test="changeNum != null ">and change_num = #{changeNum}</if>
            <if test="changeType != null  and changeType != ''">and change_type = #{changeType}</if>
            <if test="coinType != null  and coinType != ''">and coin_type = #{coinType}</if>
            <if test="exchangeStatus != null ">and exchange_status = #{exchangeStatus}</if>
            <if test="exchangeNo != null  and exchangeNo != ''">and exchange_no = #{exchangeNo}</if>
            <if test="exchangeTime != null ">and exchange_time = #{exchangeTime}</if>
            <if test="exchangeStage != null ">and exchange_stage = #{exchangeStage}</if>
            <if test="exchangeExpirationDateBefore != null ">and exchange_expiration_date_before =
                #{exchangeExpirationDateBefore}
            </if>
            <if test="exchangeExpirationDateAfter != null ">and exchange_expiration_date_after =
                #{exchangeExpirationDateAfter}
            </if>
            <if test="params!=null and params.createTimeS !=null">and aucr.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and aucr.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="params!=null and params.exchangeTimeS !=null">and aucr.exchange_time &gt;=
                #{params.exchangeTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.exchangeTimeE !=null">and aucr.exchange_time &lt;=
                #{params.exchangeTimeE}' 23:59:59'
            </if>
            <if test="userName != null and userName != ''">
                and ( u1.nick_name like concat('%', #{userName}, '%')
                or u1.user_name like concat('%', #{userName}, '%'))
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="extra != null  and extra != ''">and extra = #{extra}</if>
        </where>
        GROUP BY aucr.id order by aucr.id asc
    </select>
    <select id="getAgentByAid" resultType="java.lang.Integer">
        select id from agent where aid=#{aid}
    </select>

    <select id="selectActivityUserCoinRecordById" resultMap="ActivityUserCoinRecordResult">
        select * from ${DB_RED_BOOK_ACTIVITY}.activity_user_coin_record where id =#{id}
    </select>
    <resultMap id="activityUserMap" type="ActivityUser">
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="aid" jdbcType="VARCHAR" property="aid" />
        <result column="commemorate_coin" jdbcType="INTEGER" property="commemorateCoin" />
        <result column="renew_reward_coin_count" jdbcType="INTEGER" property="renewRewardCoinCount" />
        <result column="unfirst_renew_reward_coin_count" jdbcType="INTEGER" property="unfirstRenewRewardCoinCount" />
        <result column="first_renew_reward_coin_count" jdbcType="INTEGER" property="firstRenewRewardCoinCount" />
        <result column="invite_reward_coin_count" jdbcType="INTEGER" property="inviteRewardCoinCount" />
        <result column="invite_year_vip_count" jdbcType="INTEGER" property="inviteYearVipCount" />
        <result column="invite_one_month_count" jdbcType="INTEGER" property="inviteOneMonthCount" />
        <result column="invite_total_count" jdbcType="INTEGER" property="inviteTotalCount" />
        <result column="join_type" jdbcType="INTEGER" property="joinType" />
    </resultMap>

    <select id="selectActivityUserList" resultMap="activityUserMap">
        select user_id,aid,commemorate_coin,renew_reward_coin_count,unfirst_renew_reward_coin_count,first_renew_reward_coin_count,
        invite_reward_coin_count,invite_year_vip_count,invite_one_month_count,invite_total_count,join_type
        from ${DB_RED_BOOK_ACTIVITY}.activity_user where activity_base_id=#{activityBaseId}
    </select>
    <resultMap type="Agent" id="agentResultMap">
        <result property="id" column="id"/>
        <result property="aid" column="aid"/>
        <result property="code" column="code"/>
        <result property="areaId" column="area_id"/>
        <result property="name" column="name"/>
        <result property="alias" column="alias"/>
        <result property="parentId" column="parent_id"/>
        <result property="agreementLevel" column="agreement_level"/>
        <result property="level" column="level"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="companyName" column="company_name"/>
        <result property="taxCode" column="tax_code"/>
        <result property="personName" column="person_name"/>
        <result property="phone" column="phone"/>
        <result property="idCard" column="id_card"/>
        <result property="agreementStart" column="agreement_start"/>
        <result property="agreementEnd" column="agreement_end"/>
        <result property="accountEnd" column="account_end"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="priceScheme" column="price_scheme"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getAgentFromDb" resultMap="agentResultMap">
 select a.id,
               a.aid,
               a.code,
               a.area_id,
               a.name,
               a.alias,
               a.parent_id,
               a.agreement_level,
               a.level,
               a.status,
               a.type,
               a.company_name,
               a.tax_code,
               a.person_name,
               a.phone,
               a.id_card,
               a.agreement_start,
               a.agreement_end,
               a.account_end,
               a.contact_person,
               a.price_scheme,
               a.gift_count
        from agent a where a.aid=#{aid}
    </select>
    <resultMap id="ActivityBaseResultMap" type="com.redbook.system.domain.ActivityBase">

        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="ab_name" jdbcType="VARCHAR" property="abName" />
        <result column="ab_desc" jdbcType="VARCHAR" property="abDesc" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    </resultMap>
    <select id="selectActivityBaseById" resultMap="ActivityBaseResultMap">
        select id,ab_name,start_time ,end_time
        from ${DB_RED_BOOK_ACTIVITY}.activity_base where id=#{activityBaseId}
    </select>
    <resultMap id="contentMap" type="com.redbook.system.domain.ActivityContent">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="activity_base_id" jdbcType="INTEGER" property="activityBaseId" />
    </resultMap>
    <select id="selectRenewRecordByTimeAndLength"
            resultType="com.redbook.system.domain.ActivityTydRenewRecord">
        select id,activity_content_id as activityContentId from tyd_renew_record
        where agent_id=#{agentId} and renew_time_len=#{len} and pay_time between #{startTime} and #{endTime}
    </select>

    <update id="updateActivityUserCoinRecord" parameterType="ActivityUserCoinRecord">
        update ${DB_RED_BOOK_ACTIVITY}.activity_user_coin_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityBaseId != null">activity_base_id = #{activityBaseId},</if>
            <if test="activityContentId != null">activity_content_id = #{activityContentId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="aid != null">aid = #{aid},</if>
            <if test="aname != null">aname = #{aname},</if>
            <if test="changeNum != null">change_num = #{changeNum},</if>
            <if test="changeType != null">change_type = #{changeType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="coinType != null">coin_type = #{coinType},</if>
            <if test="exchangeStatus != null">exchange_status = #{exchangeStatus},</if>
            <if test="exchangeNo != null">exchange_no = #{exchangeNo},</if>
            <if test="exchangeTime != null">exchange_time = #{exchangeTime},</if>
            <if test="exchangeStage != null">exchange_stage = #{exchangeStage},</if>
            <if test="exchangeExpirationDateBefore != null">exchange_expiration_date_before =
                #{exchangeExpirationDateBefore},
            </if>
            <if test="exchangeExpirationDateAfter != null">exchange_expiration_date_after =
                #{exchangeExpirationDateAfter},
            </if>
            <if test="extra != null">extra = #{extra},</if>
        </trim>
        where id = #{id}
    </update>


</mapper>