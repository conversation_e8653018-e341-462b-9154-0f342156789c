<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RemoteSubscribeRecordMapper">
    
    <resultMap type="RemoteSubscribeRecord" id="RemoteSubscribeRecordResult">
        <result property="id"    column="id"    />
        <result property="areaId"    column="area_id"    />
        <result property="payAreaId"    column="pay_area_id"    />
        <result property="userId"    column="user_id"    />
        <result property="money"    column="money"    />
        <result property="month"    column="month"    />
        <result property="indentNumber"    column="indent_number"    />
        <result property="beforeDate"    column="before_date"    />
        <result property="afterDate"    column="after_date"    />
        <result property="transactionTime"    column="transaction_time"    />
        <result property="payAreaName"    column="payAreaName"    />
    </resultMap>

    <sql id="selectRemoteSubscribeRecordVo">
        select id, area_id, pay_area_id, user_id, money, month, indent_number, before_date, after_date, transaction_time from hssword_red_book_teach.remote_subscribe_record
    </sql>

    <select id="selectRemoteSubscribeRecordList" parameterType="RemoteSubscribeRecord" resultMap="RemoteSubscribeRecordResult">
        select r.id,
        r.area_id,
        r.pay_area_id,
        r.user_id,
        r.money,
        r.month,
        r.indent_number,
        r.before_date,
        r.after_date,
        r.transaction_time,
        a.name as payAreaName
        from remote_subscribe_record r
        left join agent a on a.id = r.pay_area_id
        <if test="mId != null">
            left join agent_mapping am ON a.id = am.agent_id
        </if>
        <where>
            <if test="areaId != null">
                and r.area_id = #{areaId}
            </if>
            <if test="payAreaId != null">
                and r.pay_area_id = #{payAreaId}
            </if>
            <if test="mId !=null ">
                and am.user_id = #{mId}
            </if>
        </where>
    </select>
    
    <select id="selectRemoteSubscribeRecordById" parameterType="Long" resultMap="RemoteSubscribeRecordResult">
        <include refid="selectRemoteSubscribeRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRemoteSubscribeRecord" parameterType="RemoteSubscribeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into remote_subscribe_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaId != null">area_id,</if>
            <if test="payAreaId != null">pay_area_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="money != null">money,</if>
            <if test="month != null">month,</if>
            <if test="indentNumber != null and indentNumber != ''">indent_number,</if>
            <if test="beforeDate != null">before_date,</if>
            <if test="afterDate != null">after_date,</if>
            <if test="transactionTime != null">transaction_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaId != null">#{areaId},</if>
            <if test="payAreaId != null">#{payAreaId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="money != null">#{money},</if>
            <if test="month != null">#{month},</if>
            <if test="indentNumber != null and indentNumber != ''">#{indentNumber},</if>
            <if test="beforeDate != null">#{beforeDate},</if>
            <if test="afterDate != null">#{afterDate},</if>
            <if test="transactionTime != null">#{transactionTime},</if>
         </trim>
    </insert>

    <update id="updateRemoteSubscribeRecord" parameterType="RemoteSubscribeRecord">
        update remote_subscribe_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="payAreaId != null">pay_area_id = #{payAreaId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="money != null">money = #{money},</if>
            <if test="month != null">month = #{month},</if>
            <if test="indentNumber != null and indentNumber != ''">indent_number = #{indentNumber},</if>
            <if test="beforeDate != null">before_date = #{beforeDate},</if>
            <if test="afterDate != null">after_date = #{afterDate},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRemoteSubscribeRecordById" parameterType="Long">
        delete from remote_subscribe_record where id = #{id}
    </delete>

    <delete id="deleteRemoteSubscribeRecordByIds" parameterType="String">
        delete from remote_subscribe_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>