<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ProductVirtualGoodsListMapper">
    
    <resultMap type="ProductVirtualGoodsList" id="ProductVirtualGoodsListResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="goodsInfo"    column="goods_info"    />
        <result property="status"    column="status"    />
        <result property="orderNo"    column="order_no"    />
        <result property="importTime"    column="import_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProductVirtualGoodsListVo">
        select id, product_id, goods_info, status, order_no, import_time, update_time from product_virtual_goods_list
    </sql>

    <select id="selectProductVirtualGoodsListList" parameterType="ProductVirtualGoodsList" resultMap="ProductVirtualGoodsListResult">
        <include refid="selectProductVirtualGoodsListVo"/>
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="goodsInfo != null  and goodsInfo != ''"> and goods_info = #{goodsInfo}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="importTime != null "> and import_time = #{importTime}</if>
        </where>
    </select>
    
    <select id="selectProductVirtualGoodsListById" parameterType="Integer" resultMap="ProductVirtualGoodsListResult">
        <include refid="selectProductVirtualGoodsListVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProductVirtualGoodsList" parameterType="ProductVirtualGoodsList" useGeneratedKeys="true" keyProperty="id">
        insert into product_virtual_goods_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="goodsInfo != null">goods_info,</if>
            <if test="status != null">status,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="importTime != null">import_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="goodsInfo != null">#{goodsInfo},</if>
            <if test="status != null">#{status},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="importTime != null">#{importTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProductVirtualGoodsList" parameterType="ProductVirtualGoodsList">
        update product_virtual_goods_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="goodsInfo != null">goods_info = #{goodsInfo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="importTime != null">import_time = #{importTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductVirtualGoodsListById" parameterType="Integer">
        delete from product_virtual_goods_list where id = #{id}
    </delete>

    <delete id="deleteProductVirtualGoodsListByIds" parameterType="String">
        delete from product_virtual_goods_list where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>