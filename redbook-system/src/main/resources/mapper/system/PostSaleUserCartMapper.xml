<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleUserCartMapper">
    
    <resultMap type="PostSaleUserCart" id="PostSaleUserCartResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPostSaleUserCartVo">
        select id, user_id, create_time, update_time from post_sale_user_cart
    </sql>

    <select id="selectPostSaleUserCartListByUserId" parameterType="PostSaleUserCart" resultMap="PostSaleUserCartResult">
        <include refid="selectPostSaleUserCartVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectPostSaleUserCartList" parameterType="PostSaleUserCart" resultMap="PostSaleUserCartResult">
        <include refid="selectPostSaleUserCartVo"/>
        <where>
            <if test="appletUserId != null "> and user_id = #{appletUserId}</if>
        </where>
    </select>

    <select id="selectPostSaleUserCartByUserId"  resultMap="PostSaleUserCartResult">
        <include refid="selectPostSaleUserCartVo"/>
        <where>
            <if test="appletUserId != null "> and user_id = #{appletUserId}</if>
        </where>
    </select>

    <select id="selectPostSaleUserCartById"  resultMap="PostSaleUserCartResult">
        <include refid="selectPostSaleUserCartVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleUserCart" parameterType="PostSaleUserCart" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_user_cart
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePostSaleUserCart" parameterType="PostSaleUserCart">
        update post_sale_user_cart
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleUserCartById" >
        delete from post_sale_user_cart where id = #{id}
    </delete>

    <delete id="deletePostSaleUserCartByIds" parameterType="String">
        delete from post_sale_user_cart where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>