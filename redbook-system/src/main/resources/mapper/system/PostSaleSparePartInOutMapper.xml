<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleSparePartInOutMapper">
    
    <resultMap type="PostSaleSparePartInOut" id="PostSaleSparePartInOutResult">
        <result property="id"    column="id"    />
        <result property="sparePartId"    column="spare_part_id"    />
        <result property="type"    column="type"    />
        <result property="supplyId"    column="supply_id"    />
        <result property="remark"    column="remark"    />
        <result property="inOutNum"    column="in_out_num"    />
        <result property="sparePartCost"    column="spare_part_cost"    />
        <result property="sparePartTotalCost"    column="spare_part_total_cost"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="sparePartName"    column="spare_part_name"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelName"    column="model_name"    />
        <result property="sparePartCode"    column="spare_part_code"    />
        <result property="sparePartName"    column="spare_part_name"    />
    </resultMap>

    <sql id="selectPostSaleSparePartInOutVo">
        select psspio.id, psspio.spare_part_id, psspio.type, psspio.supply_id, psspio.remark, psspio.in_out_num,psspio.create_time,
               psspio.spare_part_cost,psspio.spare_part_cost * psspio.in_out_num as spare_part_total_cost,pssp.spare_part_name,
               rtb.id as model_id,rtb.name as model_name,pssp.spare_part_code
        from post_sale_spare_part_in_out psspio
        left join post_sale_spare_part pssp on psspio.spare_part_id=pssp.id
        left join redbook_tablet_model rtb on pssp.model_id=rtb.id
    </sql>

    <select id="selectPostSaleSparePartInOutList" parameterType="PostSaleSparePartInOut" resultMap="PostSaleSparePartInOutResult">
        <include refid="selectPostSaleSparePartInOutVo"/>
        <where>  
            <if test="type != null "> and psspio.type = #{type}</if>
            <if test="modelId != null "> and pssp.model_id = #{modelId}</if>
            <if test="params!=null and params.createTimeS !=null">and psspio.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and psspio.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="sparePartCode != null and sparePartCode != ''">
                and pssp.spare_part_code like concat('%', #{sparePartCode}, '%')
            </if>
            <if test="sparePartName != null and sparePartName != ''">
                and pssp.spare_part_name like concat('%', #{sparePartName}, '%')
            </if>
            <if test="modelName != null and modelName != ''">
                and rtb.name  like concat('%', #{modelName}, '%')
            </if>
            <if test="remark != null and remark != ''">
                and psspio.remark like concat('%', #{remark}, '%')
            </if>

        </where>
        order by psspio.create_time desc
    </select>
    
    <select id="selectPostSaleSparePartInOutById" parameterType="Long" resultMap="PostSaleSparePartInOutResult">
        <include refid="selectPostSaleSparePartInOutVo"/>
        where psspio.id = #{id}
    </select>
    <select id="summaryCount" resultType="com.redbook.system.domain.PostSaleSummaryCount">
        select IFNULL(sum(spare_part_total_cost),0) as totalMoney,IFNULL(sum(in_out_num),0) as totalNum
        from post_sale_spare_part_in_out
    </select>
        
    <insert id="insertPostSaleSparePartInOut" parameterType="PostSaleSparePartInOut" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_spare_part_in_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sparePartId != null">spare_part_id,</if>
            <if test="type != null">type,</if>
            <if test="supplyId != null">supply_id,</if>
            <if test="remark != null">remark,</if>
            <if test="inOutNum != null">in_out_num,</if>
            <if test="sparePartCost != null">spare_part_cost,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="sparePartTotalCost != null">spare_part_total_cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sparePartId != null">#{sparePartId},</if>
            <if test="type != null">#{type},</if>
            <if test="supplyId != null">#{supplyId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="inOutNum != null">#{inOutNum},</if>
            <if test="sparePartCost != null">#{sparePartCost},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="sparePartTotalCost != null">#{sparePartTotalCost},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into post_sale_spare_part_in_out
        (spare_part_id, type, supply_id, remark, in_out_num, spare_part_cost,create_by,spare_part_total_cost)
        values
        <foreach collection="list" item="sparePartInOut" separator=",">
            (#{sparePartInOut.sparePartId}, #{sparePartInOut.type}, #{sparePartInOut.supplyId}, #{sparePartInOut.remark},
            #{sparePartInOut.inOutNum}, #{sparePartInOut.sparePartCost}, #{sparePartInOut.createBy},#{sparePartInOut.sparePartTotalCost})
        </foreach>
    </insert>

    <update id="updatePostSaleSparePartInOut" parameterType="PostSaleSparePartInOut">
        update post_sale_spare_part_in_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="sparePartId != null">spare_part_id = #{sparePartId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="supplyId != null">supply_id = #{supplyId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="inOutNum != null">in_out_num = #{inOutNum},</if>
            <if test="sparePartCost != null">spare_part_cost = #{sparePartCost},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="sparePartTotalCost != null">spare_part_total_cost = #{sparePartTotalCost},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleSparePartInOutById" parameterType="Long">
        delete from post_sale_spare_part_in_out where id = #{id}
    </delete>

    <delete id="deletePostSaleSparePartInOutByIds" parameterType="String">
        delete from post_sale_spare_part_in_out where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>