<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemBackMapper">
    
    <resultMap type="PostSaleRepairOrderItemBack" id="PostSaleRepairOrderItemBackResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="deviceType"    column="device_type"    />
        <result property="productTypes"    column="product_types"    />
        <result property="productOtherText"    column="product_other_text"    />
        <result property="faultDesc"    column="fault_desc"    />
        <result property="modelId"    column="model_id"    />
        <result property="productSn"    column="product_sn"    />
        <result property="model2Id"    column="model2_id"    />
        <result property="productSn2"    column="product_sn2"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemBackVo">
        select id, item_id, device_type, product_types, product_other_text, fault_desc, model_id, product_sn, model2_id, product_sn2 from post_sale_repair_order_item_back
    </sql>


    <insert id="insertPostSaleRepairOrderItemBack" parameterType="PostSaleRepairOrderItemBack" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="productTypes != null">product_types,</if>
            <if test="productOtherText != null">product_other_text,</if>
            <if test="faultDesc != null">fault_desc,</if>
            <if test="modelId != null">model_id,</if>
            <if test="productSn != null">product_sn,</if>
            <if test="model2Id != null">model2_id,</if>
            <if test="productSn2 != null">product_sn2,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="productTypes != null">#{productTypes},</if>
            <if test="productOtherText != null">#{productOtherText},</if>
            <if test="faultDesc != null">#{faultDesc},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="productSn != null">#{productSn},</if>
            <if test="model2Id != null">#{model2Id},</if>
            <if test="productSn2 != null">#{productSn2},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRepairOrderItemBack" parameterType="PostSaleRepairOrderItemBack">
        update post_sale_repair_order_item_back
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="productTypes != null">product_types = #{productTypes},</if>
            <if test="productOtherText != null">product_other_text = #{productOtherText},</if>
            <if test="faultDesc != null">fault_desc = #{faultDesc},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="productSn != null">product_sn = #{productSn},</if>
            <if test="model2Id != null">model2_id = #{model2Id},</if>
            <if test="productSn2 != null">product_sn2 = #{productSn2},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>