<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemMapper">

    <resultMap type="PostSaleRepairOrderItem" id="PostSaleRepairOrderItemResult">
        <result property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="productNo" column="product_no"/>
        <result property="ownerName" column="owner_name"/>
        <result property="deviceType" column="device_type"/>
        <result property="productTypes" column="product_types"/>
        <result property="productOtherText" column="product_other_text"/>
        <result property="faultDesc" column="fault_desc"/>
        <result property="repairPlan" column="repair_plan"/>
        <result property="abnormalHandlerType" column="abnormal_handler_type"/>
        <result property="abnormalStatement" column="abnormal_statement"/>
        <result property="rejudgmentResultType" column="rejudgment_result_type"/>
        <result property="renewType" column="renew_type"/>
        <result property="renewSn" column="renew_sn"/>
        <result property="batchId" column="batch_id"/>
        <result property="modelId" column="model_id"/>
        <result property="productSn" column="product_sn"/>
        <result property="alias" column="alias"/>
        <result property="activationDate" column="activation_date"/>
        <result property="saleDate" column="sale_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="repairDate" column="repair_date"/>
        <result property="model2Id" column="model2_id"/>
        <result property="productSn2" column="product_sn2"/>
        <result property="repairUserId" column="repair_user_id"/>
        <result property="transferFactoryId" column="transfer_factory_id"/>
        <result property="transferWorkerId" column="transfer_worker_id"/>
        <result property="isPay" column="is_pay"/>
        <result property="payTime" column="pay_time"/>
        <result property="returnProductTypes" column="return_product_types"/>
        <result property="returnOtherText" column="return_other_text"/>
        <result property="repairStatus" column="repair_status"/>
        <result property="scrapStatus" column="scrap_status"/>
        <result property="abandonStatus" column="abandon_status"/>
        <result property="faultClassifyId" column="faultClassifyId"/>
        <result property="faultClassifyParentId" column="faultClassifyParentId"/>
        <result property="returnExpressOrderId" column="return_express_order_id"/>
        <result property="returnExpressNo" column="return_express_no"/>
        <result property="returnExpressCompanyId" column="return_express_company_id"/>
        <result property="isScreenBad" column="is_screen_bad"/>
        <result property="isBackCoverBad" column="is_back_cover_bad"/>
        <result property="judgeFaultDesc" column="judge_fault_desc"/>
        <result property="firstRepairActivationDate" column="first_repair_activation_date"/>
        <result property="laborFee" column="labor_fee"/>
        <result property="materialFee" column="material_fee"/>
        <result property="sendExpressFee" column="send_express_fee"/>
        <result property="returnExpressFee" column="return_express_fee"/>
<!--        <result property="expressFee" column="express_fee"/>-->
        <result property="totalFee" column="total_fee"/>
        <result property="isConfirmScrap" column="is_confirm_scrap"/>
        <result property="isConfirmAbandon" column="is_confirm_abandon"/>
        <result property="priceDissentStatus" column="price_dissent_status"/>
        <result property="isInvalid" column="is_invalid"/>

        <!--额外-->
        <result property="sendRepairNo" column="sendRepairNo"/>
        <result property="modelName" column="modelName"/>
        <result property="batchName" column="batchName"/>
        <result property="level1FaultClassifyName" column="level1FaultClassifyName"/>
        <result property="level2FaultClassifyName" column="level2FaultClassifyName"/>
        <result property="paySmsSendTime"    column="pay_sms_send_time"    />
        <result property="isNeedSendPaySms"    column="is_need_send_pay_sms"    />
        <!--发送短信用-->
        <result property="sendUserName"    column="send_user_name"    />
        <result property="sendUserPhone"    column="send_user_phone"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemVo">
        select *
        from post_sale_repair_order_item psroi
    </sql>

    <select id="selectPostSaleRepairOrderItemList" parameterType="PostSaleRepairOrderItem"
            resultMap="PostSaleRepairOrderItemResult">
        <include refid="selectPostSaleRepairOrderItemVo"/>
        <where>
            <if test="orderId != null ">and order_id = #{orderId}</if>
            <if test="productNo != null  and productNo != ''">and product_no = #{productNo}</if>
            <if test="ownerName != null  and ownerName != ''">and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="deviceType != null ">and device_type = #{deviceType}</if>
            <if test="productTypes != null  and productTypes != ''">and product_types = #{productTypes}</if>
            <if test="productOtherText != null  and productOtherText != ''">and product_other_text =
                #{productOtherText}
            </if>
            <if test="faultDesc != null  and faultDesc != ''">and fault_desc = #{faultDesc}</if>
            <if test="repairPlan != null ">and repair_plan = #{repairPlan}</if>
            <if test="abnormalHandlerType != null ">and abnormal_handler_type = #{abnormalHandlerType}</if>
            <if test="abnormalStatement != null  and abnormalStatement != ''">and abnormal_statement =
                #{abnormalStatement}
            </if>
            <if test="rejudgmentResultType != null ">and rejudgment_result_type = #{rejudgmentResultType}</if>
            <if test="renewType != null ">and renew_type = #{renewType}</if>
            <if test="renewSn != null  and renewSn != ''">and renew_sn = #{renewSn}</if>
            <if test="batchId != null ">and batch_id = #{batchId}</if>
            <if test="modelId != null ">and model_id = #{modelId}</if>
            <if test="productSn != null  and productSn != ''">and product_sn = #{productSn}</if>
            <if test="activationDate != null ">and activation_date = #{activationDate}</if>
            <if test="expirationDate != null ">and expiration_date = #{expirationDate}</if>
            <if test="repairDate != null ">and repair_date = #{repairDate}</if>
            <if test="model2Id != null  and model2Id != ''">and model2_id = #{model2Id}</if>
            <if test="productSn2 != null  and productSn2 != ''">and product_sn2 = #{productSn2}</if>
            <if test="repairUserId != null  and repairUserId != ''">and repair_user_id = #{repairUserId}</if>
            <if test="transferFactoryId != null  and transferFactoryId != ''">and transfer_factory_id =
                #{transferFactoryId}
            </if>
            <if test="transferWorkerId != null  and transferWorkerId != ''">and transfer_worker_id =
                #{transferWorkerId}
            </if>
            <if test="isPay != null ">and is_pay = #{isPay}</if>
            <if test="payTime != null ">and pay_time = #{payTime}</if>
            <if test="returnProductTypes != null  and returnProductTypes != ''">and return_product_types =
                #{returnProductTypes}
            </if>
            <if test="returnOtherText != null  and returnOtherText != ''">and return_other_text = #{returnOtherText}
            </if>
            <if test="repairStatus != null ">and repair_status = #{repairStatus}</if>
            <if test="returnExpressNo != null  and returnExpressNo != ''">and return_express_no = #{returnExpressNo}
            </if>
            <if test="returnExpressCompanyId != null  and returnExpressCompanyId != ''">and return_express_company_id =
                #{returnExpressCompanyId}
            </if>
        </where>
    </select>
    <select id="selectPostSaleRepairOrderItemListFromApplet"
            resultType="com.redbook.system.domain.PostSaleRepairOrderItem">
        select  psroi.id as id,psroi.order_id as orderId, psroi.create_time as createTime,psroi.scrap_status as scrapStatus,psroi.abandon_status as abandonStatus,psroi.price_dissent_status as priceDissentStatus,
        psroi.owner_name as ownerName,psroi.repair_status as repairStatus,psroi.alias,psroi.sale_date as saleDate,
        rtm.`name` as modelName,psroi.product_sn as productSn
        from post_sale_repair_order_item psroi
        left join  post_sale_repair_order psro on psroi.order_id = psro.id
        left join  redbook_tablet_model rtm on psroi.model_id=rtm.id
        <where>
            psro.create_by=#{createBy} and psroi.is_invalid=0
            <if test="searchValue != null and searchValue != ''">
                and (psroi.product_sn like concat('%', #{searchValue}, '%')
                or psroi.owner_name like concat('%', #{searchValue}, '%')
                or psro.send_express_no like concat('%', #{searchValue}, '%')
                or psro.send_user_phone like concat('%', #{searchValue}, '%')
                )
            </if>
            <if test="params != null and params.createTimeS != null">
                and psroi.create_time &gt;=#{params.createTimeS}' 00:00:00'
            </if>
            <if test="params != null and params.createTimeE != null">
                and psroi.create_time &lt;=#{params.createTimeE}' 23:59:59'
            </if>
        </where>
        order by psroi.id desc limit #{offset},#{pageSize}
    </select>
    <select id="selectPostSaleRepairOrderItemById" parameterType="integer" resultMap="PostSaleRepairOrderItemResult">
        select  psroi.*,psro.send_user_name,psro.send_user_phone
        from post_sale_repair_order_item psroi
                 left join  post_sale_repair_order psro  on psroi.order_id=psro.id where psroi.id=#{itemId}
    </select>
    <select id="selectPostSaleRepairOrderItemByIds" resultMap="PostSaleRepairOrderItemResult">
        select  psroi.*,psro.send_user_name,psro.send_user_phone
        from post_sale_repair_order_item psroi
        left join  post_sale_repair_order psro  on psroi.order_id=psro.id where psroi.id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="selectByOrderId" parameterType="integer" resultMap="PostSaleRepairOrderItemResult">
        <include refid="selectPostSaleRepairOrderItemVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectFaultList" resultMap="PostSaleRepairOrderItemResult">
        SELECT psroi.*,psro. send_repair_no as sendRepairNo,rtm.`name` as modelName,rtb.Batch_Name as batchName,
        psfc.classify_name as level2FaultClassifyName,psfcP.classify_name as level1FaultClassifyName,
        psroif.fault_classify_id as faultClassifyId,psroif.fault_classify_parent_id as faultClassifyParentId
        from post_sale_repair_order_item psroi
        left join post_sale_repair_order psro on psroi.order_id=psro.id
        left join redbook_tablet_model rtm on psroi.model_id=rtm.id
        left join redbook_tablet_batch rtb on rtb.model_id=rtm.id
        left join post_sale_repair_order_item_faults psroif on psroi.id=psroif.item_id
        left join post_sale_fault_classify psfc on psroif.fault_classify_id=psfc.id
        left join post_sale_fault_classify psfcP on psfc.parent_id=psfcP.id
        <where>
            <if test="modelIdList != null and modelIdList.size() > 0">
                AND psroi.model_id IN
                <foreach item="id" collection="modelIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="batchIdList != null and batchIdList.size() > 0">
                AND psroi.batch_id IN
                <foreach item="id" collection="batchIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="faultClassifyIdListAll != null and faultClassifyIdListAll.size() > 0">
                AND psroif.fault_classify_id IN
                <foreach item="id" collection="faultClassifyIdListAll" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="rejudgmentResultTypeList != null and rejudgmentResultTypeList.size() > 0">
                AND psroi.rejudgment_result_type IN
                <foreach item="id" collection="rejudgmentResultTypeList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="dynamicsSql != null and dynamicsSql !='' ">
                and ${dynamicsSql}
            </if>
        </where>
        GROUP BY psroi.id
    </select>

    <insert id="insertPostSaleRepairOrderItem" parameterType="PostSaleRepairOrderItem" useGeneratedKeys="true"
            keyProperty="id">
        insert into post_sale_repair_order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="productNo != null">product_no,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="productTypes != null">product_types,</if>
            <if test="productOtherText != null">product_other_text,</if>
            <if test="faultDesc != null">fault_desc,</if>
            <if test="repairPlan != null">repair_plan,</if>
            <if test="abnormalHandlerType != null">abnormal_handler_type,</if>
            <if test="abnormalStatement != null">abnormal_statement,</if>
            <if test="rejudgmentResultType != null">rejudgment_result_type,</if>
            <if test="renewType != null">renew_type,</if>
            <if test="renewSn != null">renew_sn,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="productSn != null">product_sn,</if>
            <if test="alias != null">alias,</if>
            <if test="activationDate != null">activation_date,</if>
            <if test="saleDate != null">sale_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="repairDate != null">repair_date,</if>
            <if test="model2Id != null">model2_id,</if>
            <if test="productSn2 != null">product_sn2,</if>
            <if test="repairUserId != null">repair_user_id,</if>
            <if test="transferFactoryId != null">transfer_factory_id,</if>
            <if test="transferWorkerId != null">transfer_worker_id,</if>
            <if test="isPay != null">is_pay,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="returnProductTypes != null">return_product_types,</if>
            <if test="returnOtherText != null">return_other_text,</if>
            <if test="repairStatus != null">repair_status,</if>
            <if test="scrapStatus != null">scrap_status,</if>
            <if test="abandonStatus != null">abandon_status,</if>
            <if test="firstRepairActivationDate != null">first_repair_activation_date,</if>
            <if test="isRepaired != null">is_repaired,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="productTypes != null">#{productTypes},</if>
            <if test="productOtherText != null">#{productOtherText},</if>
            <if test="faultDesc != null">#{faultDesc},</if>
            <if test="repairPlan != null">#{repairPlan},</if>
            <if test="abnormalHandlerType != null">#{abnormalHandlerType},</if>
            <if test="abnormalStatement != null">#{abnormalStatement},</if>
            <if test="rejudgmentResultType != null">#{rejudgmentResultType},</if>
            <if test="renewType != null">#{renewType},</if>
            <if test="renewSn != null">#{renewSn},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="productSn != null">#{productSn},</if>
            <if test="alias != null">#{alias},</if>
            <if test="activationDate != null">#{activationDate},</if>
            <if test="saleDate != null">#{saleDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="repairDate != null">#{repairDate},</if>
            <if test="model2Id != null">#{model2Id},</if>
            <if test="productSn2 != null">#{productSn2},</if>
            <if test="repairUserId != null">#{repairUserId},</if>
            <if test="transferFactoryId != null">#{transferFactoryId},</if>
            <if test="transferWorkerId != null">#{transferWorkerId},</if>
            <if test="isPay != null">#{isPay},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="returnProductTypes != null">#{returnProductTypes},</if>
            <if test="returnOtherText != null">#{returnOtherText},</if>
            <if test="repairStatus != null">#{repairStatus},</if>
            <if test="scrapStatus != null">#{scrapStatus},</if>
            <if test="abandonStatus != null">#{abandonStatus},</if>
            <if test="firstRepairActivationDate != null">#{firstRepairActivationDate},</if>
            <if test="isRepaired != null">#{isRepaired},</if>
        </trim>
    </insert>

    <update id="updatePostSaleRepairOrderItem" parameterType="PostSaleRepairOrderItem">
        update post_sale_repair_order_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="productTypes != null">product_types = #{productTypes},</if>
            <if test="productOtherText != null">product_other_text = #{productOtherText},</if>
            <if test="faultDesc != null">fault_desc = #{faultDesc},</if>
            <if test="repairPlan != null">repair_plan = #{repairPlan},</if>
            <if test="abnormalHandlerType != null">abnormal_handler_type = #{abnormalHandlerType},</if>
            <if test="abnormalStatement != null">abnormal_statement = #{abnormalStatement},</if>
            <if test="rejudgmentResultType != null">rejudgment_result_type = #{rejudgmentResultType},</if>
            <if test="renewType != null">renew_type = #{renewType},</if>
            <if test="renewSn != null">renew_sn = #{renewSn},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="productSn != null">product_sn = #{productSn},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="activationDate != null">activation_date = #{activationDate},</if>
            <if test="saleDate != null">sale_date = #{saleDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="repairDate != null">repair_date = #{repairDate},</if>
            <if test="model2Id != null">model2_id = #{model2Id},</if>
            <if test="productSn2 != null">product_sn2 = #{productSn2},</if>
            <if test="repairUserId != null">repair_user_id = #{repairUserId},</if>
            <if test="transferFactoryId != null">transfer_factory_id = #{transferFactoryId},</if>
            <if test="transferWorkerId != null">transfer_worker_id = #{transferWorkerId},</if>
            <if test="isPay != null">is_pay = #{isPay},</if>
            <if test="isConfirmScrap != null">is_confirm_scrap = #{isConfirmScrap},</if>
            <if test="isConfirmAbandon != null">is_confirm_abandon = #{isConfirmAbandon},</if>
            <if test="priceDissentStatus != null">price_dissent_status = #{priceDissentStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="returnProductTypes != null">return_product_types = #{returnProductTypes},</if>
            <if test="returnOtherText != null">return_other_text = #{returnOtherText},</if>
            <if test="repairStatus != null">repair_status = #{repairStatus},</if>
            <if test="scrapStatus != null">scrap_status = #{scrapStatus},</if>
            <if test="abandonStatus != null">abandon_status = #{abandonStatus},</if>
            <if test="returnExpressOrderId != null">return_express_order_id = #{returnExpressOrderId},</if>
            <if test="returnExpressNo != null">return_express_no = #{returnExpressNo},</if>
            <if test="returnExpressCompanyId != null">return_express_company_id = #{returnExpressCompanyId},</if>
            <if test="isScreenBad != null">is_screen_bad = #{isScreenBad},</if>
            <if test="isBackCoverBad != null">is_back_cover_bad = #{isBackCoverBad},</if>
            <if test="judgeFaultDesc != null">judge_fault_desc = #{judgeFaultDesc},</if>
            <if test="isNeedSendPaySms != null">is_need_send_pay_sms = #{isNeedSendPaySms},</if>
            <if test="paySmsSendTime != null">pay_sms_send_time = #{paySmsSendTime},</if>
            <if test="firstRepairActivationDate != null">first_repair_activation_date = #{firstRepairActivationDate},</if>
            <if test="returnTime != null">return_time = #{returnTime},</if>
            <if test="laborFee != null">labor_fee = #{laborFee},</if>
            <if test="materialFee != null">material_fee = #{materialFee},</if>
<!--            <if test="expressFee != null">express_fee = #{expressFee},</if>-->
            <if test="sendExpressFee != null">send_express_fee = #{sendExpressFee},</if>
            <if test="returnExpressFee != null">return_express_fee = #{returnExpressFee},</if>
            <if test="totalFee != null">total_fee = #{totalFee},</if>
            <if test="isInvalid != null">is_invalid = #{isInvalid},</if>
        </trim>
        where id = #{id}
    </update>


    <select id="selectNeedPayOrderList" resultMap="PostSaleRepairOrderItemResult">
        select  psroi.id,psro.product_no,psro.send_user_name,psro.send_user_phone
        from post_sale_repair_order_item psroi
        left join  post_sale_repair_order psro  on psroi.order_id=psro.id
        where repair_status = 2 and is_need_send_pay_sms=1 and (pay_sms_send_time is not null and pay_sms_send_time &lt;= #{localDateTime})
        limit #{pageSize} offset #{offset}
    </select>
    <select id="selectNeedAutoCompletionList" resultType="com.redbook.system.domain.PostSaleRepairOrderItem">
        SELECT id,order_id as orderId
        FROM post_sale_repair_order_item
        WHERE repair_status = #{status} AND return_time IS NOT NULL AND return_time &lt;= NOW() - INTERVAL ${days} DAY
    </select>

    <update id="updateNeedAutoCompletionRecordByItemIds">
        update post_sale_repair_order_item
        set repair_status = 9
        where id in
        <foreach collection="list" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </update>
    <update id="itemReturnCancel">
        update post_sale_repair_order_item
        set repair_status = #{repairStatus},return_express_order_id=null,return_express_no=null,return_express_company_id=null,return_time=null,scrap_status=#{scrapStatus},abandon_status=#{abandonStatus}
        where id = #{itemId}
    </update>
</mapper>