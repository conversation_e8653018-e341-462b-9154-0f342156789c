<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemFlowMapper">
    
    <resultMap type="PostSaleRepairOrderItemFlow" id="PostSaleRepairOrderItemFlowResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="actionType"    column="action_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemFlowVo">
        select id, item_id, action_type, create_by, create_time, status, remark from post_sale_repair_order_item_flow
    </sql>

    <select id="selectPostSaleRepairOrderItemFlowList" parameterType="PostSaleRepairOrderItemFlow" resultMap="PostSaleRepairOrderItemFlowResult">
        <include refid="selectPostSaleRepairOrderItemFlowVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="actionType != null "> and action_type = #{actionType}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectPostSaleRepairOrderItemFlowById" parameterType="Long" resultMap="PostSaleRepairOrderItemFlowResult">
        <include refid="selectPostSaleRepairOrderItemFlowVo"/>
        where id = #{id}
    </select>
    <select id="selectByItemId" resultType="com.redbook.system.domain.PostSaleRepairOrderItemFlow">
        select * from post_sale_repair_order_item_flow where item_id = #{itemId} order by id asc
    </select>

    <insert id="insertPostSaleRepairOrderItemFlow" parameterType="PostSaleRepairOrderItemFlow" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="actionType != null">action_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="userType != null">user_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="actionType != null">#{actionType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="userType != null">#{userType},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRepairOrderItemFlow" parameterType="PostSaleRepairOrderItemFlow">
        update post_sale_repair_order_item_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="actionType != null">action_type = #{actionType},</if>
            <if test="createUserId != null">create_by = #{createUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleRepairOrderItemFlowById" parameterType="Long">
        delete from post_sale_repair_order_item_flow where id = #{id}
    </delete>

    <delete id="deletePostSaleRepairOrderItemFlowByIds" parameterType="String">
        delete from post_sale_repair_order_item_flow where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>