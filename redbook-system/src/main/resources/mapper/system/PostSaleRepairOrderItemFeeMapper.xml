<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemFeeMapper">
    
    <resultMap type="PostSaleRepairOrderItemFee" id="PostSaleRepairOrderItemFeeResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="laborFee"    column="labor_fee"    />
        <result property="materialFee"    column="material_fee"    />
        <result property="sendExpressFee" column="send_express_fee"/>
        <result property="returnExpressFee" column="return_express_fee"/>
<!--        <result property="expressFee"    column="express_fee"    />-->
        <result property="totalFee"    column="total_fee"    />
        <result property="status"    column="status"    />
        <result property="isRefund"    column="is_refund"    />
        <result property="payOrderId"    column="pay_order_id"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemFeeVo">
        select id, item_id, labor_fee, material_fee, /*express_fee,*/send_express_fee,return_express_fee, total_fee, status,is_refund,pay_order_id from post_sale_repair_order_item_fee
    </sql>

    <select id="selectUnPayListByItemIdList" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and status = 0
    </select>
    <select id="selectUnPayListByItemId" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id =#{itemId} and status = 0
    </select>

    <select id="selectPayItemFee" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where pay_order_id = #{payOrderId}
    </select>


    <select id="selectByPayOrderId" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where pay_order_id = #{payOrderId}
    </select>

    <select id="selectByItemId" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id = #{itemId} and is_refund=0 and status = 1 order by id asc limit 1
    </select>

    <select id="selectUnPayRecordByItemId" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id = #{itemId} and is_refund=0 and status = 0  order by id desc limit 1
    </select>
    <select id="selectItemPayFeeList" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id = #{itemId} and is_refund=0 and status = 1 order by id asc
    </select>
    <select id="selectPostSaleRepairOrderItemFeeListByItemIds" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="selectPostSaleRepairOrderItemFeeListByItemId" resultMap="PostSaleRepairOrderItemFeeResult">
        select * from post_sale_repair_order_item_fee where item_id =#{itemId}
    </select>

    <insert id="insertPostSaleRepairOrderItemFee" parameterType="PostSaleRepairOrderItemFee" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_fee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="laborFee != null">labor_fee,</if>
            <if test="materialFee != null">material_fee,</if>
<!--            <if test="expressFee != null">express_fee,</if>-->
            <if test="sendExpressFee != null">send_express_fee,</if>
            <if test="returnExpressFee != null">return_express_fee,</if>
            <if test="totalFee != null">total_fee,</if>
            <if test="status != null">status,</if>
            <if test="isRefund != null">is_refund,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="laborFee != null">#{laborFee},</if>
            <if test="materialFee != null">#{materialFee},</if>
<!--            <if test="expressFee != null">#{expressFee},</if>-->
            <if test="sendExpressFee != null">#{sendExpressFee},</if>
            <if test="returnExpressFee != null">#{returnExpressFee},</if>
            <if test="totalFee != null">#{totalFee},</if>
            <if test="status != null">#{status},</if>
            <if test="isRefund != null">#{isRefund},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRepairOrderItemFee" parameterType="PostSaleRepairOrderItemFee">
        update post_sale_repair_order_item_fee
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="laborFee != null">labor_fee = #{laborFee},</if>
            <if test="materialFee != null">material_fee = #{materialFee},</if>
<!--       <if test="expressFee != null">express_fee = #{expressFee},</if>-->
            <if test="sendExpressFee != null">send_express_fee = #{sendExpressFee},</if>
            <if test="returnExpressFee != null">return_express_fee = #{returnExpressFee},</if>
            <if test="totalFee != null">total_fee = #{totalFee},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isRefund != null">is_refund = #{isRefund},</if>
            <if test="payOrderId != null">pay_order_id = #{payOrderId},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>