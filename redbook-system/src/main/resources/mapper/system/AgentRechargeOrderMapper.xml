<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AgentRechargeOrderMapper">
    
    <resultMap type="AgentRechargeOrder" id="AgentRechargeOrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="transactionType"    column="transaction_type"    />
        <result property="fundType"    column="fund_type"    />
        <result property="agentId"    column="agent_id"    />
        <result property="money"    column="money"    />
        <result property="giftMoney"    column="gift_money"    />
        <result property="payMode"    column="pay_mode"    />
        <result property="orderDesc"    column="order_desc"    />
        <result property="tradeNo"    column="trade_no"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="agentName"    column="name"    />
        <result property="contactPerson"    column="user_name"    />
        <result property="agentLevel"    column="level"    />
        <result property="status"    column="status"    />
        <result property="invoiceStatus"    column="invoice_status"    />
    </resultMap>

    <sql id="selectAgentRechargeOrderVo">
        select DISTINCT aro.id,
               aro.order_id,
               aro.fund_type,
               aro.agent_id,
               aro.money,
               aro.gift_money,
               aro.pay_mode,
               aro.order_desc,
               aro.trade_no,
               aro.order_status,
               aro.remark,
               aro.create_time,
               aro.update_time,
               a.name,
               u2.nick_name as user_name,
               a.level,
               a.status,
               ati.transaction_type
        from agent_recharge_order aro
                 LEFT JOIN agent_transaction_info ati ON aro.order_id = ati.indent_number
                 LEFT JOIN agent a ON a.id = aro.agent_id
                 left join sys_user u2 ON a.contact_person = u2.user_id
    </sql>

    <select id="selectAgentRechargeOrderList" parameterType="AgentRechargeOrder" resultMap="AgentRechargeOrderResult">
        <include refid="selectAgentRechargeOrderVo"/>
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>
            a.parent_id=-1
            and aro.order_status=1
            <if test="orderId != null  and orderId != ''">and aro.order_id = #{orderId}</if>
            <if test="fundType != null ">and aro.fund_type = #{fundType}</if>
            <if test="agentId != null ">and aro.agent_id = #{agentId}</if>
            <if test="money != null ">and aro.money = #{money}</if>
            <if test="payMode != null ">and aro.pay_mode = #{payMode}</if>
            <if test="invoiceStatus != null ">and aro.invoice_status = #{invoiceStatus}</if>
            <if test="orderDesc != null  and orderDesc != ''">and aro.order_desc = #{orderDesc}</if>
            <if test="tradeNo != null  and tradeNo != ''">and aro.trade_no = #{tradeNo}</if>
            <if test="orderStatus != null ">and aro.order_status = #{orderStatus}</if>
            <if test="remark != null and remark != '' != null ">and aro.remark  like concat('%', #{remark}, '%')</if>
            <if test="mId != null and agentIdList.size==0">and am.user_id = #{mId}</if>
            <if test="params!=null and params.createTimeS !=null">and aro.create_time >=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and aro.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="startDate!=null and startDate != ''">and aro.create_time >=#{startDate}' 00:00:00'
            </if>
            <if test="endDate!=null and endDate != ''">and aro.create_time &lt;=#{endDate}' 23:59:59'
            </if>
            <if test="rechargeIdList!=null and rechargeIdList.size>0">
                and aro.id in
                <foreach collection="rechargeIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectAgentRechargeOrderById" parameterType="Long" resultMap="AgentRechargeOrderResult">
        <include refid="selectAgentRechargeOrderVo"/>
        where aro.id = #{id}
    </select>
        
    <insert id="insertAgentRechargeOrder" parameterType="AgentRechargeOrder" useGeneratedKeys="true" keyProperty="id">
        insert into agent_recharge_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">order_id,</if>
            <if test="fundType != null">fund_type,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="money != null">money,</if>
            <if test="giftMoney != null">gift_money,</if>
            <if test="payMode != null">pay_mode,</if>
            <if test="orderDesc != null">order_desc,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="remark != null and remark != '' != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">#{orderId},</if>
            <if test="fundType != null">#{fundType},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="money != null">#{money},</if>
            <if test="giftMoney != null">#{giftMoney},</if>
            <if test="payMode != null">#{payMode},</if>
            <if test="orderDesc != null">#{orderDesc},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="remark != null and remark != '' != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAgentRechargeOrder" parameterType="AgentRechargeOrder">
        update agent_recharge_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null and orderId != ''">order_id = #{orderId},</if>
            <if test="fundType != null">fund_type = #{fundType},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="money != null">money = #{money},</if>
            <if test="giftMoney != null">gift_money = #{giftMoney},</if>
            <if test="payMode != null">pay_mode = #{payMode},</if>
            <if test="orderDesc != null">order_desc = #{orderDesc},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="remark != null and remark != '' != null">remark=#{remark},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentRechargeOrderById" parameterType="Long">
        delete from agent_recharge_order where id = #{id}
    </delete>

    <delete id="deleteAgentRechargeOrderByIds" parameterType="String">
        delete from agent_recharge_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAgentRechargeOrderByOrderId" resultType="com.redbook.system.domain.AgentRechargeOrder">
        select * from agent_recharge_order where order_id = #{orderId}
    </select>
</mapper>