<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleQuestionClassifyMapper">
    
    <resultMap type="PostSaleQuestionClassify" id="PostSaleQuestionClassifyResult">
        <result property="id"    column="id"    />
        <result property="classifyName"    column="classify_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="articleNum"    column="article_num"    />
    </resultMap>

    <sql id="selectPostSaleQuestionClassifyVo">
        select id, classify_name, create_time, create_by, update_time, update_by, is_delete, article_num from post_sale_question_classify
    </sql>

    <select id="selectPostSaleQuestionClassifyList" parameterType="PostSaleQuestionClassify" resultMap="PostSaleQuestionClassifyResult">
        <include refid="selectPostSaleQuestionClassifyVo"/>
        <where>
            is_delete=0
            <if test="classifyName != null  and classifyName != ''"> and classify_name like concat('%', #{classifyName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="articleNum != null "> and article_num = #{articleNum}</if>
        </where>
    </select>
    
    <select id="selectPostSaleQuestionClassifyById" parameterType="Long" resultMap="PostSaleQuestionClassifyResult">
        <include refid="selectPostSaleQuestionClassifyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleQuestionClassify" parameterType="PostSaleQuestionClassify" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_question_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="articleNum != null">article_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">#{classifyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="articleNum != null">#{articleNum},</if>
         </trim>
    </insert>

    <update id="updatePostSaleQuestionClassify" parameterType="PostSaleQuestionClassify">
        update post_sale_question_classify
        <trim prefix="SET" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name = #{classifyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="articleNum != null">article_num = #{articleNum},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateArticleNumById">
        update post_sale_question_classify set article_num = article_num + #{num},update_by=#{userId} where id = #{id}
    </update>

    <delete id="deletePostSaleQuestionClassifyById" parameterType="Long">
        update post_sale_question_classify set is_delete=1,update_by=#{userId} where id = #{id}
    </delete>

    <delete id="deletePostSaleQuestionClassifyByIds" parameterType="String">
        update post_sale_question_classify set is_delete=1,update_by=#{userId} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>