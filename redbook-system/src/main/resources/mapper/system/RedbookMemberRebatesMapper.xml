<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RedbookMemberRebatesMapper">
    
    <resultMap type="RedbookMemberRebates" id="RedbookMemberRebatesResult">
        <result property="id"    column="id"    />
        <result property="salesRangeMin"    column="sales_range_min"    />
        <result property="salesRangeMax"    column="sales_range_max"    />
        <result property="rebatePercentage"    column="rebate_percentage"    />
        <result property="rebatePrice"    column="rebate_price"    />
        <result property="levelName"    column="level_name"    />
    </resultMap>

    <sql id="selectRedbookMemberRebatesVo">
        select id, sales_range_min, sales_range_max, rebate_percentage, rebate_price, level_name from redbook_member_rebates
    </sql>

    <select id="selectRedbookMemberRebatesList" parameterType="RedbookMemberRebates" resultMap="RedbookMemberRebatesResult">
        <include refid="selectRedbookMemberRebatesVo"/>
        <where>  
            <if test="salesRangeMin != null "> and sales_range_min = #{salesRangeMin}</if>
            <if test="salesRangeMax != null "> and sales_range_max = #{salesRangeMax}</if>
            <if test="rebatePercentage != null "> and rebate_percentage = #{rebatePercentage}</if>
            <if test="rebatePrice != null "> and rebate_price = #{rebatePrice}</if>
            <if test="levelName != null  and levelName != ''"> and level_name like concat('%', #{levelName}, '%')</if>
        </where>
    </select>
    
    <select id="selectRedbookMemberRebatesById" parameterType="Long" resultMap="RedbookMemberRebatesResult">
        <include refid="selectRedbookMemberRebatesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRedbookMemberRebates" parameterType="RedbookMemberRebates" useGeneratedKeys="true" keyProperty="id">
        insert into redbook_member_rebates
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="salesRangeMin != null">sales_range_min,</if>
            <if test="salesRangeMax != null">sales_range_max,</if>
            <if test="rebatePercentage != null">rebate_percentage,</if>
            <if test="rebatePrice != null">rebate_price,</if>
            <if test="levelName != null and levelName != ''">level_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="salesRangeMin != null">#{salesRangeMin},</if>
            <if test="salesRangeMax != null">#{salesRangeMax},</if>
            <if test="rebatePercentage != null">#{rebatePercentage},</if>
            <if test="rebatePrice != null">#{rebatePrice},</if>
            <if test="levelName != null and levelName != ''">#{levelName},</if>
         </trim>
    </insert>

    <update id="updateRedbookMemberRebates" parameterType="RedbookMemberRebates">
        update redbook_member_rebates
        <trim prefix="SET" suffixOverrides=",">
            <if test="salesRangeMin != null">sales_range_min = #{salesRangeMin},</if>
            <if test="salesRangeMax != null">sales_range_max = #{salesRangeMax},</if>
            <if test="rebatePercentage != null">rebate_percentage = #{rebatePercentage},</if>
            <if test="rebatePrice != null">rebate_price = #{rebatePrice},</if>
            <if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRedbookMemberRebatesById" parameterType="Long">
        delete from redbook_member_rebates where id = #{id}
    </delete>

    <delete id="deleteRedbookMemberRebatesByIds" parameterType="String">
        delete from redbook_member_rebates where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>