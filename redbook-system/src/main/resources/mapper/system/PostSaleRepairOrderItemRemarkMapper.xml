<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemRemarkMapper">
    
    <resultMap type="PostSaleRepairOrderItemRemark" id="PostSaleRepairOrderItemRemarkResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="remark"    column="remark"    />
        <result property="extra"    column="extra"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemRemarkVo">
        select id, item_id, remark, extra, type, create_time from post_sale_repair_order_item_remark
    </sql>

    <insert id="insertPostSaleRepairOrderItemRemark" parameterType="PostSaleRepairOrderItemRemark" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_remark
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="remark != null">remark,</if>
            <if test="extra != null">extra,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="extra != null">#{extra},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>
    <select id="selectByItemIdAndType" resultMap="PostSaleRepairOrderItemRemarkResult">
        select id, item_id, remark, extra, type, create_time from post_sale_repair_order_item_remark where item_id = #{itemId} and type = #{remarkType} order by id desc limit 1
    </select>

</mapper>