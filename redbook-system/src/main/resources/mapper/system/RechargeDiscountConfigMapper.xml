<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RechargeDiscountConfigMapper">
    
    <resultMap type="RechargeDiscountConfig" id="RechargeDiscountConfigResult">
        <result property="fundtypeid"    column="fundtypeid"    />
        <result property="money"    column="money"    />
        <result property="gift"    column="gift"    />
    </resultMap>

    <sql id="selectRechargeDiscountConfigVo">
        select fundtypeid, money, gift from recharge_discount_config
    </sql>

    <select id="selectRechargeDiscountConfigByFundtypeid" parameterType="Long" resultMap="RechargeDiscountConfigResult">
        <include refid="selectRechargeDiscountConfigVo"/>
        where fundtypeid = #{fundtypeid}

    </select>

        
    <insert id="insertRechargeDiscountConfig" parameterType="RechargeDiscountConfig">
        insert into recharge_discount_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fundtypeid != null">fundtypeid,</if>
            <if test="money != null">money,</if>
            <if test="gift != null">gift,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fundtypeid != null">#{fundtypeid},</if>
            <if test="money != null">#{money},</if>
            <if test="gift != null">#{gift},</if>
         </trim>
    </insert>

    <update id="updateRechargeDiscountConfig" parameterType="RechargeDiscountConfig">
        update recharge_discount_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="money != null">money = #{money},</if>
            <if test="gift != null">gift = #{gift},</if>
        </trim>
        where fundtypeid = #{fundtypeid}
    </update>

    <delete id="deleteRechargeDiscountConfigByFundtypeid" parameterType="Long">
        delete from recharge_discount_config where fundtypeid = #{fundtypeid}
    </delete>

    <delete id="deleteRechargeDiscountConfigByFundtypeids" parameterType="String">
        delete from recharge_discount_config where fundtypeid in
        <foreach item="fundtypeid" collection="array" open="(" separator="," close=")">
            #{fundtypeid}
        </foreach>
    </delete>
</mapper>