<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemMessageMapper">
    
    <resultMap type="PostSaleRepairOrderItemMessage" id="PostSaleRepairOrderItemMessageResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="message"    column="message"    />
        <result property="createBy"    column="create_by"    />
        <result property="userType"    column="user_type"    />
        <result property="appletUserReadStatus"    column="applet_user_read_status"    />
        <result property="judgeUserReadStatus"    column="judge_user_read_status"    />
        <result property="signUserReadStatus"    column="sign_user_read_status"    />
        <result property="repairUserReadStatus"    column="repair_user_read_status"    />
        <result property="qcUserReadStatus"    column="qc_user_read_status"    />
        <result property="updateTime"    column="update_time"    />
        <!--追加-->
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderMessageVo">
        select psroim.id, psroim.item_id, psroim.create_time, psroim.message, psroim.create_by, psroim.user_type, psroim.applet_user_read_status, psroim.judge_user_read_status, psroim.sign_user_read_status,
               psroim.repair_user_read_status, psroim.qc_user_read_status,
               CASE
                   WHEN psroim.user_type = 0 THEN psu.nickname
                   WHEN psroim.user_type = 1 THEN su.nick_name
                   ELSE NULL
                   END AS user_name
        from post_sale_repair_order_item_message psroim
                 left join post_sale_user psu on psroim.create_by = psu.id and psroim.user_type = 0
                 left join sys_user su on psroim.create_by = su.user_id and psroim.user_type = 1
    </sql>
    <update id="updateReadStatusByItemIdAndColumn">
        update post_sale_repair_order_item_message set ${column} = #{status} where item_id = #{itemId}
    </update>


    <select id="selectPostSaleRepairOrderItemMessagesByItemId" resultType="com.redbook.system.domain.PostSaleRepairOrderItemMessage">
        <include refid="selectPostSaleRepairOrderMessageVo"/>
        where item_id = #{itemId}
    </select>
    <select id="selectPostSaleRepairOrderItemMessagesByItemIds"
            resultType="com.redbook.system.domain.PostSaleRepairOrderItemMessage">
        <include refid="selectPostSaleRepairOrderMessageVo"/>
        where item_id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>

    <insert id="insertPostSaleRepairOrderItemMessage" parameterType="PostSaleRepairOrderItemMessage" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="message != null">message,</if>
            <if test="createBy != null">create_by,</if>
            <if test="userType != null">user_type,</if>
            <if test="appletUserReadStatus != null">applet_user_read_status,</if>
            <if test="judgeUserReadStatus != null">judge_user_read_status,</if>
            <if test="signUserReadStatus != null">sign_user_read_status,</if>
            <if test="repairUserReadStatus != null">repair_user_read_status,</if>
            <if test="qcUserReadStatus != null">qc_user_read_status,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="message != null">#{message},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="userType != null">#{userType},</if>
            <if test="appletUserReadStatus != null">#{appletUserReadStatus},</if>
            <if test="judgeUserReadStatus != null">#{judgeUserReadStatus},</if>
            <if test="signUserReadStatus != null">#{signUserReadStatus},</if>
            <if test="repairUserReadStatus != null">#{repairUserReadStatus},</if>
            <if test="qcUserReadStatus != null">#{qcUserReadStatus},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>


    <delete id="deletePostSaleRepairOrderMessageById" parameterType="Long">
        delete from post_sale_repair_order_item_message where id = #{id}
    </delete>

    <delete id="deletePostSaleRepairOrderMessageByIds" parameterType="String">
        delete from post_sale_repair_order_item_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>