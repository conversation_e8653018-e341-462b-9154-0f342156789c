<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ConsultPictureFolderMapper">
    
    <resultMap type="ConsultPictureFolder" id="ConsultPictureFolderResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="aid"    column="aid"    />
        <result property="exclusiveShopId"    column="exclusive_shop_id"    />
        <result property="pictureUrl"    column="picture_url"    />
        <result property="isHide"    column="is_hide"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectConsultPictureFolderVo">
        select id, agent_id, aid,exclusive_shop_id, picture_url, is_hide, sort, create_time from consult_picture_folder
    </sql>

    <select id="systemPictureFolderList" resultMap="ConsultPictureFolderResult">
        select id, agent_id, aid,exclusive_shop_id, picture_url, is_hide, sort, create_time
        from consult_picture_folder
        where sys_type = 1
        order by sort asc
    </select>

    <select id="countSystemPictureNum" resultType="int">
        select  count(1)
        from consult_picture_folder
        where sys_type = 2 and picture_url like '%introduce%'
        and agent_id = #{agentId}
        <if test="exclusiveShopId != null  and exclusiveShopId != ''"> and exclusive_shop_id = #{exclusiveShopId}</if>

    </select>

    <select id="selectConsultPictureFolderList" parameterType="ConsultPictureFolder" resultMap="ConsultPictureFolderResult">
        <include refid="selectConsultPictureFolderVo"/>
        <where>
            and sys_type = 2
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="aid != null  and aid != ''"> and aid = #{aid}</if>
            <if test="exclusiveShopId != null  and exclusiveShopId != ''"> and exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="pictureUrl != null  and pictureUrl != ''"> and picture_url = #{pictureUrl}</if>
            <if test="isHide != null "> and is_hide = #{isHide}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
        order by sort is null asc,sort asc
    </select>
    
    <select id="selectConsultPictureFolderById" resultMap="ConsultPictureFolderResult">
        <include refid="selectConsultPictureFolderVo"/>
        where id = #{id} and sys_type = 2
    </select>
        
    <insert id="insertConsultPictureFolder" parameterType="ConsultPictureFolder" useGeneratedKeys="true" keyProperty="id">
        insert into consult_picture_folder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sysType != null">sys_type,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="aid != null and aid != ''">aid,</if>
            <if test="exclusiveShopId != null and exclusiveShopId != ''">exclusive_shop_id,</if>
            <if test="pictureUrl != null">picture_url,</if>
            <if test="isHide != null">is_hide,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sysType != null">#{sysType},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="aid != null and aid != ''">#{aid},</if>
            <if test="exclusiveShopId != null and exclusiveShopId != ''">#{exclusiveShopId},</if>
            <if test="pictureUrl != null">#{pictureUrl},</if>
            <if test="isHide != null">#{isHide},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        insert into consult_picture_folder(sys_type,agent_id,aid,exclusive_shop_id,picture_url,is_hide,sort)
        values
        <foreach item="item" collection="list" separator=",">
            (#{item.sysType},#{item.agentId},#{item.aid},#{item.exclusiveShopId},#{item.pictureUrl},#{item.isHide},#{item.sort})
        </foreach>
    </insert>

    <update id="updateConsultPictureFolder" parameterType="ConsultPictureFolder">
        update consult_picture_folder
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="aid != null and aid != ''">aid = #{aid},</if>
            <if test="exclusiveShopId != null and exclusiveShopId != ''">exclusive_shop_id = #{exclusiveShopId},</if>
            <if test="pictureUrl != null">picture_url = #{pictureUrl},</if>
            <if test="isHide != null">is_hide = #{isHide},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConsultPictureFolderById">
        delete from consult_picture_folder where id = #{id} and sys_type = 2
    </delete>

    <delete id="deleteConsultPictureFolderByIds" parameterType="String">
        delete from consult_picture_folder where sys_type = 2 and id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>