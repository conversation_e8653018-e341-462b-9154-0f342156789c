<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleUserCartItemMapper">
    
    <resultMap type="PostSaleUserCartItem" id="PostSaleUserCartItemResult">
        <result property="id"    column="id"    />
        <result property="cartId"    column="cart_id"    />
        <result property="goodId"    column="good_id"    />
        <result property="goodName"    column="good_name"    />
        <result property="goodNum"    column="good_num"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPostSaleUserCartItemVo">
        select id, cart_id, good_id, good_name,good_num, total_money, create_time, update_time from post_sale_user_cart_item
    </sql>

    <select id="selectPostSaleUserCartItemList" resultMap="PostSaleUserCartItemResult">
        select item.id, item.cart_id, item.good_id, item.good_name, item.good_num, item.total_money, item.create_time, item.update_time
        from post_sale_user_cart cart
        inner join post_sale_user_cart_item item on cart.id = item.cart_id
        <where>
            <if test="appletUserId != null "> and cart.user_id = #{appletUserId}</if>
        </where>
    </select>
    
    <select id="selectPostSaleUserCartItem" resultMap="PostSaleUserCartItemResult">
        <include refid="selectPostSaleUserCartItemVo"/>
        where cart_id = #{cartId} and good_id = #{goodId}
    </select>

    <select id="selectPostSaleUserCartItemById" resultMap="PostSaleUserCartItemResult">
        <include refid="selectPostSaleUserCartItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertPostSaleUserCartItem" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_user_cart_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cartId != null">cart_id,</if>
            <if test="goodId != null">good_id,</if>
            <if test="goodName != null">good_name,</if>
            <if test="goodNum != null">good_num,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cartId != null">#{cartId},</if>
            <if test="goodId != null">#{goodId},</if>
            <if test="goodName != null">#{goodName},</if>
            <if test="goodNum != null">#{goodNum},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertPostSaleUserCartItem">
        insert into post_sale_user_cart_item (cart_id, good_id, good_num, total_money, create_time, update_time) values
        <foreach item="item" collection="list" separator=",">
            (#{item.cartId},#{item.goodId},#{item.goodNum},#{item.totalMoney},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <update id="updatePostSaleUserCartItem" parameterType="PostSaleUserCartItem">
        update post_sale_user_cart_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="cartId != null">cart_id = #{cartId},</if>
            <if test="goodId != null">good_id = #{goodId},</if>
            <if test="goodName != null">good_name = #{goodName},</if>
            <if test="goodNum != null">good_num = #{goodNum},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleUserCartItemByCartId">
        delete from post_sale_user_cart_item where cart_id = #{cartId}
    </delete>

    <delete id="deletePostSaleUserCartItemById">
        delete from post_sale_user_cart_item where id = #{id}
    </delete>

    <delete id="deleteByUserId">
        delete t from post_sale_user_cart_item t
        inner join post_sale_user_cart c on t.cart_id = c.id
        where c.user_id = #{userId}
    </delete>

    <delete id="deletePostSaleUserCartItemByIds" parameterType="String">
        delete from post_sale_user_cart_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>