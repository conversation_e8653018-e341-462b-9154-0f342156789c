<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRefundOrderMapper">
    
    <resultMap type="PostSaleRefundOrder" id="PostSaleRefundOrderResult">
        <result property="id"    column="id"    />
        <result property="bizType"    column="biz_type"    />
        <result property="bizOrderId"    column="biz_order_id"    />
        <result property="refundOrderNo"    column="refund_order_no"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="status"    column="status"    />
        <result property="refundId"    column="refund_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="refundSuccessTime"    column="refund_success_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectPostSaleRefundOrderVo">
        select id, biz_type, biz_order_id, refund_order_no, refund_amount, status, refund_id, apply_time, handle_time, refund_reason, refund_success_time, create_time, create_by
        from post_sale_refund_order
    </sql>

    <select id="selectPostSaleRefundOrderList" parameterType="PostSaleRefundOrder" resultMap="PostSaleRefundOrderResult">
        <include refid="selectPostSaleRefundOrderVo"/>
        <where>  
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="bizOrderId != null "> and biz_order_id = #{bizOrderId}</if>
            <if test="refundOrderNo != null  and refundOrderNo != ''"> and refund_order_no = #{refundOrderNo}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="refundId != null  and refundId != ''"> and refund_id = #{refundId}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="handleTime != null "> and handle_time = #{handleTime}</if>
            <if test="refundReason != null  and refundReason != ''"> and refund_reason = #{refundReason}</if>
            <if test="refundSuccessTime != null "> and refund_success_time = #{refundSuccessTime}</if>
        </where>
    </select>
    
    <select id="selectPostSaleRefundOrderById"  resultMap="PostSaleRefundOrderResult">
        <include refid="selectPostSaleRefundOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectPostSaleRefundOrder"  resultMap="PostSaleRefundOrderResult">
        <include refid="selectPostSaleRefundOrderVo"/>
        where refund_order_no = #{refundOrderNo}
    </select>

    <insert id="insertPostSaleRefundOrder" parameterType="PostSaleRefundOrder" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_refund_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">biz_type,</if>
            <if test="bizOrderId != null">biz_order_id,</if>
            <if test="refundOrderNo != null">refund_order_no,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="status != null">status,</if>
            <if test="refundId != null">refund_id,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="refundSuccessTime != null">refund_success_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">#{bizType},</if>
            <if test="bizOrderId != null">#{bizOrderId},</if>
            <if test="refundOrderNo != null">#{refundOrderNo},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="refundId != null">#{refundId},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="refundSuccessTime != null">#{refundSuccessTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRefundOrder" parameterType="PostSaleRefundOrder">
        update post_sale_refund_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="bizType != null and bizType != ''">biz_type = #{bizType},</if>
            <if test="bizOrderId != null">biz_order_id = #{bizOrderId},</if>
            <if test="refundOrderNo != null">refund_order_no = #{refundOrderNo},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="refundSuccessTime != null">refund_success_time = #{refundSuccessTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleRefundOrderById" >
        delete from post_sale_refund_order where id = #{id}
    </delete>

    <delete id="deletePostSaleRefundOrderByIds" parameterType="String">
        delete from post_sale_refund_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>