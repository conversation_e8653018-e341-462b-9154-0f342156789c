<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityCodeExchangeMapper">

    <resultMap type="ActivityCodeExchange" id="ActivityCodeExchangeResult">
        <result property="id" column="id"/>
        <result property="codeNo" column="code_no"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="userAddr" column="user_addr"/>
        <result property="status" column="status"/>
        <result property="exchangeTime" column="exchange_time"/>
        <result property="exchangeStage" column="exchange_stage"/>
        <result property="exchangeExpirationDateBefore" column="exchange_expiration_date_before"/>
        <result property="exchangeExpirationDateAfter" column="exchange_expiration_date_after"/>
    </resultMap>

    <sql id="selectActivityCodeExchangeVo">
        select ace.id, ace.code_no, ace.user_id,ace.user_addr, ace.status, ace.exchange_time, ace.exchange_stage, ace.exchange_expiration_date_before, ace.exchange_expiration_date_after,
         u1.user_name
         from ${DB_RED_BOOK_ACTIVITY}.activity_code_exchange ace
         left join ${DB_RED_BOOK}.user_info u1 on ace.user_id=u1.user_id
    </sql>

    <select id="selectActivityCodeExchangeList" parameterType="ActivityCodeExchange"
            resultMap="ActivityCodeExchangeResult">
        <include refid="selectActivityCodeExchangeVo"/>
        <where>
            <if test="codeNo != null  and codeNo != ''">and ace.code_no = #{codeNo}</if>
            <if test="userId != null  and userId != ''">and ace.user_id = #{userId}</if>
            <if test="status != null ">and ace.status = #{status}</if>
            <if test="exchangeTime != null ">and ace.exchange_time = #{exchangeTime}</if>
            <if test="exchangeStage != null ">and ace.exchange_stage = #{exchangeStage}</if>
            <if test="exchangeExpirationDateBefore != null ">and ace.exchange_expiration_date_before =
                #{exchangeExpirationDateBefore}
            </if>
            <if test="exchangeExpirationDateAfter != null ">and ace.exchange_expiration_date_after =
                #{exchangeExpirationDateAfter}
            </if>
            <if test="searchValue != null and searchValue != ''">
                and (u1.nick_name like concat('%', #{searchValue}, '%')
                or u1.user_name like concat('%', #{searchValue}, '%'))
            </if>
        </where>
    </select>

    <update id="updateActivityCodeExchange" parameterType="ActivityCodeExchange">
        update ${DB_RED_BOOK_ACTIVITY}.activity_code_exchange
        <trim prefix="SET" suffixOverrides=",">
            <if test="codeNo != null and codeNo != ''">code_no = #{codeNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="exchangeTime != null">exchange_time = #{exchangeTime},</if>
            <if test="exchangeStage != null">exchange_stage = #{exchangeStage},</if>
            <if test="exchangeExpirationDateBefore != null">exchange_expiration_date_before =
                #{exchangeExpirationDateBefore},
            </if>
            <if test="exchangeExpirationDateAfter != null">exchange_expiration_date_after =
                #{exchangeExpirationDateAfter},
            </if>
        </trim>
        where id = #{id}
    </update>
</mapper>