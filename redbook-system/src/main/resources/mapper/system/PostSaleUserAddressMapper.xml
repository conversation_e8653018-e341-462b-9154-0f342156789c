<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleUserAddressMapper">
    
    <resultMap type="PostSaleUserAddress" id="PostSaleUserAddressResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="userExtensionNo"    column="user_extension_no"    />
        <result property="userProvince"    column="user_province"    />
        <result property="userCity"    column="user_city"    />
        <result property="userCounty"    column="user_county"    />
        <result property="userAddress"    column="user_address"    />
        <result property="organization"    column="organization"    />
        <result property="isDefault"    column="is_default"    />
    </resultMap>

    <sql id="selectPostSaleUserAddressVo">
        select id, user_id, user_name, user_phone, user_extension_no, user_province, user_city, user_county, organization,user_address, is_default from post_sale_user_address
    </sql>

    <select id="selectPostSaleUserAddressList" parameterType="PostSaleUserAddress" resultMap="PostSaleUserAddressResult">
        <include refid="selectPostSaleUserAddressVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and user_phone = #{userPhone}</if>
            <if test="userExtensionNo != null  and userExtensionNo != ''"> and user_extension_no = #{userExtensionNo}</if>
            <if test="userProvince != null  and userProvince != ''"> and user_province = #{userProvince}</if>
            <if test="userCity != null  and userCity != ''"> and user_city = #{userCity}</if>
            <if test="userCounty != null  and userCounty != ''"> and user_county = #{userCounty}</if>
            <if test="userAddress != null  and userAddress != ''"> and user_address = #{userAddress}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
            <if test="searchValue != null and searchValue != ''">
                and (user_name like concat('%', #{searchValue}, '%')
                or user_phone like concat('%', #{searchValue}, '%')
                or user_extension_no like concat('%', #{searchValue}, '%')
                or user_address like concat('%', #{searchValue}, '%')
                )
            </if>
        </where>
        order by is_default desc
    </select>
    
    <select id="selectPostSaleUserAddressById" parameterType="Long" resultMap="PostSaleUserAddressResult">
        <include refid="selectPostSaleUserAddressVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleUserAddress" parameterType="PostSaleUserAddress" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_user_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="userExtensionNo != null">user_extension_no,</if>
            <if test="userProvince != null">user_province,</if>
            <if test="userCity != null">user_city,</if>
            <if test="userCounty != null">user_county,</if>
            <if test="userAddress != null">user_address,</if>
            <if test="organization != null">organization,</if>
            <if test="isDefault != null">is_default,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="userExtensionNo != null">#{userExtensionNo},</if>
            <if test="userProvince != null">#{userProvince},</if>
            <if test="userCity != null">#{userCity},</if>
            <if test="userCounty != null">#{userCounty},</if>
            <if test="userAddress != null">#{userAddress},</if>
            <if test="organization != null">#{organization},</if>
            <if test="isDefault != null">#{isDefault},</if>
         </trim>
    </insert>

    <update id="updateUserIsDefault">
        update post_sale_user_address set is_default = 0
        where user_id = #{userId} and is_default = 1
    </update>
    <update id="updatePostSaleUserAddress" parameterType="PostSaleUserAddress">
        update post_sale_user_address
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userPhone != null">user_phone = #{userPhone},</if>
            <if test="userExtensionNo != null">user_extension_no = #{userExtensionNo},</if>
            <if test="userProvince != null">user_province = #{userProvince},</if>
            <if test="userCity != null">user_city = #{userCity},</if>
            <if test="userCounty != null">user_county = #{userCounty},</if>
            <if test="userAddress != null">user_address = #{userAddress},</if>
            <if test="organization != null">organization = #{organization},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleUserAddressById" parameterType="Long">
        delete from post_sale_user_address where id = #{id}
    </delete>

    <delete id="deletePostSaleUserAddressByIds" parameterType="String">
        delete from post_sale_user_address where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>