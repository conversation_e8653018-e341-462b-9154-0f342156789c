<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityUserFormDataMapper">

    <resultMap type="ActivityUserFormData" id="ActivityUserFormDataResult">
        <result property="id" column="id"/>
        <result property="activityBaseId" column="activity_base_id"/>
        <result property="name" column="name"/>
        <result property="userWxOpenid" column="user_wx_openid"/>
        <result property="address" column="address"/>
        <result property="contact" column="contact"/>
        <result property="agentName" column="agentName"/>
        <result property="fromUserName" column="fromUserName"/>
        <result property="isRedbookVip" column="is_redbook_vip"/>
        <result property="agentId" column="agent_id"/>
        <result property="fromUserId" column="from_user_id"/>
        <collection property="experienceCodeList" javaType="java.util.ArrayList"
                    ofType="String" select="selectExperienceCodeListByOpendId" column="user_wx_openid"/>

    </resultMap>
    <select id="selectExperienceCodeListByOpendId" parameterType="String" resultType="java.lang.String" >
		select code
		from ${DB_RED_BOOK_ACTIVITY}.activity_user_experience_code
		where user_wx_openid=#{userWxOpenid}
	</select>
    <sql id="selectActivityUserFormDataVo">
        select aufd.id, aufd.activity_base_id, aufd.user_wx_openid, aufd.name, aufd.address,
        aufd.contact, aufd.is_redbook_vip, aufd.agent_id, aufd.from_user_id,
        a.name as agentName,u1.user_name as fromUserName,case is_redbook_vip when 1 then '是' else '否' end as isRedbookVipStr
         from  ${DB_RED_BOOK_ACTIVITY}.activity_user_form_data aufd
         left join agent a on aufd.agent_id = a.id
         LEFT JOIN agent_mapping am ON a.id = am.agent_id
         LEFT JOIN sys_user u ON am.user_id = u.user_id
         left join sys_user u2 ON a.contact_person = u2.user_id
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=aufd.from_user_id
    </sql>

    <select id="selectActivityUserFormDataList" parameterType="ActivityUserFormData"
            resultMap="ActivityUserFormDataResult">
        <include refid="selectActivityUserFormDataVo"/>
        <where>
            <if test="activityBaseId != null ">and  aufd.activity_base_id = #{activityBaseId}</if>
            <if test="name != null  and name != ''">and aufd.name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''">and aufd.address = #{address}</if>
            <if test="isRedbookVip != null ">and aufd.is_redbook_vip = #{isRedbookVip}</if>
            <if test="agentId != null ">and aufd.agent_id = #{agentId}</if>
            <if test="fromUserId != null  and fromUserId != ''">and aufd.from_user_id = #{fromUserId}</if>
            <if test="mId != null">
                and am.user_id = #{mId}
            </if>
            <if test="agentStatus != null">
                <if test="agentStatus ==1 ">and aufd.agent_id is not null</if>
                <if test="agentStatus ==2 ">and aufd.agent_id is null</if>
            </if>
        </where>
        GROUP BY aufd.id order by aufd.id asc
    </select>

    <select id="selectActivityUserFormDataById" parameterType="Long" resultMap="ActivityUserFormDataResult">
        <include refid="selectActivityUserFormDataVo"/>
        where id = #{id}
    </select>

    <update id="updateActivityUserFormData" parameterType="ActivityUserFormData">
        update  ${DB_RED_BOOK_ACTIVITY}.activity_user_form_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityBaseId != null">activity_base_id = #{activityBaseId},</if>
            <if test="userWxOpenid != null">user_wx_openid = #{userWxOpenid},</if>
            <if test="name != null">name = #{name},</if>
            <if test="address != null">address = #{address},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="isRedbookVip != null">is_redbook_vip = #{isRedbookVip},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>