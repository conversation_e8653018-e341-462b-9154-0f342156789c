<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ExclusiveShopTransactionInfoMapper">
    
    <resultMap type="ExclusiveShopTransactionInfo" id="ExclusiveShopTransactionInfoResult">
        <result property="id"    column="id"    />
        <result property="exclusiveShopId"    column="exclusive_shop_id"    />
        <result property="indentNumber"    column="indent_number"    />
        <result property="transactionType"    column="transaction_type"    />
        <result property="money"    column="money"    />
        <result property="paymentType"    column="payment_type"    />
        <result property="balance"    column="balance"    />
        <result property="transactionTime"    column="transaction_time"    />
        <result property="remark"    column="remark"    />
        <result property="screenshots"    column="screenshots"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExclusiveShopTransactionInfoVo">
        select id, exclusive_shop_id, indent_number, transaction_type, money, payment_type, balance, transaction_time, remark, screenshots, create_time, update_time from exclusive_shop_transaction_info
    </sql>

    <select id="selectExclusiveShopTransactionInfoList" parameterType="ExclusiveShopTransactionInfo" resultMap="ExclusiveShopTransactionInfoResult">
        <include refid="selectExclusiveShopTransactionInfoVo"/>
        <where>  
            <if test="exclusiveShopId != null "> and exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="indentNumber != null  and indentNumber != ''"> and indent_number = #{indentNumber}</if>
            <if test="transactionType != null  and transactionType != ''"> and transaction_type = #{transactionType}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="paymentType != null "> and payment_type = #{paymentType}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="transactionTime != null "> and transaction_time = #{transactionTime}</if>
            <if test="screenshots != null  and screenshots != ''"> and screenshots = #{screenshots}</if>
            <if test="params!=null and params.transactionTimeS !=null">and transaction_time >=#{params.transactionTimeS}' 00:00:00' </if>
            <if test="params!=null and params.transactionTimeE !=null">and transaction_time &lt;=#{params.transactionTimeE}' 23:59:59' </if>
        </where>
    </select>
    
    <select id="selectExclusiveShopTransactionInfoById" parameterType="Long" resultMap="ExclusiveShopTransactionInfoResult">
        <include refid="selectExclusiveShopTransactionInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertExclusiveShopTransactionInfo" parameterType="ExclusiveShopTransactionInfo" useGeneratedKeys="true" keyProperty="id">
        insert into exclusive_shop_transaction_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exclusiveShopId != null">exclusive_shop_id,</if>
            <if test="indentNumber != null">indent_number,</if>
            <if test="transactionType != null and transactionType != ''">transaction_type,</if>
            <if test="money != null">money,</if>
            <if test="paymentType != null">payment_type,</if>
            <if test="balance != null">balance,</if>
            <if test="transactionTime != null">transaction_time,</if>
            <if test="remark != null">remark,</if>
            <if test="screenshots != null">screenshots,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="exclusiveShopId != null">#{exclusiveShopId},</if>
            <if test="indentNumber != null">#{indentNumber},</if>
            <if test="transactionType != null and transactionType != ''">#{transactionType},</if>
            <if test="money != null">#{money},</if>
            <if test="paymentType != null">#{paymentType},</if>
            <if test="balance != null">#{balance},</if>
            <if test="transactionTime != null">#{transactionTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="screenshots != null">#{screenshots},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateExclusiveShopTransactionInfo" parameterType="ExclusiveShopTransactionInfo">
        update exclusive_shop_transaction_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="exclusiveShopId != null">exclusive_shop_id = #{exclusiveShopId},</if>
            <if test="indentNumber != null">indent_number = #{indentNumber},</if>
            <if test="transactionType != null and transactionType != ''">transaction_type = #{transactionType},</if>
            <if test="money != null">money = #{money},</if>
            <if test="paymentType != null">payment_type = #{paymentType},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="screenshots != null">screenshots = #{screenshots},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExclusiveShopTransactionInfoById" parameterType="Long">
        delete from exclusive_shop_transaction_info where id = #{id}
    </delete>

    <delete id="deleteExclusiveShopTransactionInfoByIds" parameterType="String">
        delete from exclusive_shop_transaction_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>