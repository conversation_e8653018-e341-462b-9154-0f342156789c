<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderFileMapper">
    
    <resultMap type="PostSaleRepairOrderFile" id="PostSaleRepairOrderFileResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="extra"    column="extra"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderFileVo">
        select id, order_id, item_id, file_type, extra from post_sale_repair_order_file
    </sql>

    <select id="selectPostSaleRepairOrderFileListByOrderId" resultType="com.redbook.system.domain.dto.postSale.bean.PostSaleRepairOrderFileEntity">
        select psrof.id,psrof.`extra`,psrof.file_type as fileType
        from post_sale_repair_order_file psrof
        where order_id=#{orderId} and item_id is null
    </select>

    <select id="selectPostSaleRepairOrderItemFileListByItemId" resultType="com.redbook.system.domain.dto.postSale.bean.PostSaleRepairOrderItemFileEntity">
        select psrof.id,psrof.`extra`as itemExtra,psrof.file_type as itemFileType
        from post_sale_repair_order_file psrof
        where item_id=#{id}
    </select>
    <select id="selectPostSaleRepairOrderItemFileListByItemIds" resultType="com.redbook.system.domain.dto.postSale.bean.PostSaleRepairOrderItemFileEntity">
        select psrof.id,psrof.item_id as itemId,psrof.`extra`as itemExtra,psrof.file_type as itemFileType
        from post_sale_repair_order_file psrof
        where item_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insertPostSaleRepairOrderFile" parameterType="PostSaleRepairOrderFile" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="extra != null">extra,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="extra != null">#{extra},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into post_sale_repair_order_file (order_id, item_id, file_type, extra) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.orderId}, #{item.itemId}, #{item.fileType}, #{item.extra})
        </foreach>
    </insert>

</mapper>