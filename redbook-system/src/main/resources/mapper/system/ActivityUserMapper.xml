<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityUserMapper">
    <resultMap id="ActivityUserResult" type="ActivityUser">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="user_addr" jdbcType="VARCHAR" property="userAddr" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="totalDonateNum" jdbcType="INTEGER" property="totalDonateNum" />
        <result column="stage" jdbcType="INTEGER" property="stage" />
        <result column="au_turntable_count" jdbcType="INTEGER" property="auTurntableCount"/>
        <result column="cumulative_total_days" jdbcType="INTEGER" property="cumulativeTotalDays"/>
    </resultMap>

    <!--1小学/2初中/3高中/4大学/5 出国/11小升初/21 初升高-->
    <sql id="selectActivityUserVo">
        select
        au.id,au.user_id, au.user_addr, u1.user_name, au.cloud_artian_coin as totalDonateNum,
         CASE u1.stage
		WHEN 1 THEN
		'小学'
		WHEN 2 THEN
		'初中'
		WHEN 3 THEN
		'高中'
		WHEN 4 THEN
		'大学'
		WHEN 5 THEN
		'出国'
		WHEN 11 THEN
		'小升初'
		WHEN 21 THEN
		'初升高'
	    END AS stageDesc,au.au_turntable_count,au.cumulative_total_days
         from  ${DB_RED_BOOK_ACTIVITY}.activity_user au
         left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
         left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>

    <select id="selectActivityUserList" parameterType="ActivityUser" resultMap="ActivityUserResult">
        <include refid="selectActivityUserVo"/>
        <where>
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="userId != null  and userId != ''">and au.user_id = #{userId}</if>
            <if test="userAddr != null  and userAddr != ''">and au.user_addr = #{userAddr}</if>
            <if test="stage != null ">and u1.stage = #{stage}</if>
            <if test="params!=null and params.createTimeS !=null">and au.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and au.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="userName != null and userName != ''">
                and ( au.user_name like concat('%', #{userName}, '%')
                or u1.user_name like concat('%', #{userName}, '%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        GROUP BY au.id order by au.cloud_artian_coin desc,au.create_time asc
    </select>

    <sql id="selectSmallDcwActivityUserVo">
        select
        au.id,au.user_id, au.user_addr, u1.user_name,a.`alias` as user_alias,aucr.`extra`,au.total_word_count,au.personal_reward,
        CASE u1.stage
        WHEN 1 THEN
        '小学'
        WHEN 2 THEN
        '初中'
        WHEN 3 THEN
        '高中'
        WHEN 4 THEN
        '大学'
        WHEN 5 THEN
        '出国'
        WHEN 11 THEN
        '小升初'
        WHEN 21 THEN
        '初升高'
        END AS stageDesc,aucr.create_time as coinCreateTime,aucr.change_type as changeType,aucr.exchange_no
        from
         ${DB_RED_BOOK_ACTIVITY}.activity_user_coin_record aucr
        left join   ${DB_RED_BOOK_ACTIVITY}.activity_user au on au.user_id=aucr.user_id

        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectSmallDcwRecordList" resultType="com.redbook.system.domain.ActivityUser">
        <include refid="selectSmallDcwActivityUserVo"/>
         <where>
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="activityBaseId != null ">and aucr.activity_base_id = #{activityBaseId}</if>
            <if test="userId != null  and userId != ''">and au.user_id = #{userId}</if>
            <if test="userAddr != null  and userAddr != ''">and au.user_addr = #{userAddr}</if>
            <if test="stage != null ">and u1.stage = #{stage}</if>
            <if test="params!=null and params.createTimeS !=null">and aucr.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and aucr.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="userName != null and userName != ''">
                and ( au.user_name like concat('%', #{userName}, '%')
                or u1.user_name like concat('%', #{userName}, '%'))
            </if>
             <if test="exchangeNo != null and exchangeNo != ''">
                 and  aucr.exchange_no like concat('%', #{exchangeNo}, '%')
             </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
    </select>
    <resultMap id="BaseDrawRecordResultMap" type="com.redbook.system.domain.ActivityDrawRecord">
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="ag_name" jdbcType="VARCHAR" property="agName"/>
        <result column="ag_type" jdbcType="INTEGER" property="agType"/>
        <result column="ag_amount" jdbcType="INTEGER" property="agAmount"/>
        <result column="createDateStr" jdbcType="VARCHAR" property="createDateStr"/>
    </resultMap>
    <select id="selectRewardRecordListByUserId" resultMap="BaseDrawRecordResultMap">
        select  a.id, a.activity_gift_id,a.create_time,b.ag_name,b.ag_type,a.ag_amount,DATE_FORMAT(a.create_date,'%Y.%m.%d') as createDateStr
        from ${DB_RED_BOOK_ACTIVITY}.activity_draw_record a
                 left join ${DB_RED_BOOK_ACTIVITY}.activity_gift b on a.activity_gift_id=b.id
        where a.activity_base_id=#{activityBaseId} and a.activity_content_id=#{activityContentId}
          and  a.user_id=#{userId} order by a.id desc
    </select>
    <select id="selectUserWordStudyRecordList"
            resultType="com.redbook.system.domain.ActivityUserWordDayRecord">
        select a.word_count wordCount,a.create_date createDate
        from ${DB_RED_BOOK_ACTIVITY}.activity_user_word_day_record a where  a.activity_base_id=#{activityBaseId} and a.user_id=#{userId} order by id asc

    </select>
    <select id="selectActivityNameById" resultType="java.lang.String">
        select a.ab_name from ${DB_RED_BOOK_ACTIVITY}.activity_base a where a.id=#{activityBaseId}
    </select>

    <select id="fiveStarUserRecordList" resultType="com.redbook.system.domain.FiveStarUserRecord">
        select
        ui.user_name userName,
        ui.user_id userId,
        ui.aid aid,
        ui.exclusive_shop_id exclusiveShopId,
        ut.before_expiration_date exchangeExpirationDateBefore,
        ut.after_expiration_date exchangeExpirationDateAfter,
        ut.stage exchangeStage,
        ut.time_status timeStatus,
        ut.utime utime,
        a.name agentName,
        ui.stage1_expiration_date stage1ExpirationDate,
        ui.stage2_expiration_date stage2ExpirationDate,
        ui.stage3_expiration_date stage3ExpirationDate,
        ui.stage4_expiration_date stage4ExpirationDate,
        ui.stage5_expiration_date stage5ExpirationDate,
        ui.stage11_expiration_date stage11ExpirationDate,
        ui.stage21_expiration_date stage21ExpirationDate,
        ui.stage
        from
            ${DB_RED_BOOK}.user_temp ut
        left join ${DB_RED_BOOK}.user_info ui on ui.user_id=ut.user_id
        left join agent a on a.aid=ui.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on ui.exclusive_shop_id=es.id
        <where>
            <if test="userId != null  and userId != ''">and ut.user_id = #{userId}</if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>

    </select>

    <select id="fiveStarUserRecord" resultType="com.redbook.system.domain.FiveStarUserRecord">
        select
        ui.user_name userName,
        ui.user_id userId,
        ui.aid aid,
        ui.exclusive_shop_id exclusiveShopId,
        ut.stage stage,
        ut.before_expiration_date exchangeExpirationDateBefore,
        ut.after_expiration_date exchangeExpirationDateAfter,
        ut.stage exchangeStage,
        ut.time_status timeStatus
        from
        ${DB_RED_BOOK}.user_temp ut
        left join ${DB_RED_BOOK}.user_info ui on ui.user_id=ut.user_id
        left join agent a on a.aid=ui.aid
        <where>
            <if test="userId != null  and userId != ''">and ut.user_id = #{userId}</if>
        </where>
    </select>

    <update id="exchangeFiveStar">
        update ${DB_RED_BOOK}.user_temp set stage=#{exchangeStage}, time_status=1,before_expiration_date=#{renewBeforeDate},after_expiration_date=#{renewAfterDate}
        where user_id=#{userId}
    </update>
</mapper>