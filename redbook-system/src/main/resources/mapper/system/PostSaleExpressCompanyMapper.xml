<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleExpressCompanyMapper">
    
    <resultMap type="PostSaleExpressCompany" id="PostSaleExpressCompanyResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="displayOrder"    column="display_order"    />
        <result property="isAblePick"    column="is_able_pick"    />
    </resultMap>

    <sql id="selectPostSaleExpressCompanyVo">
        select id, name, is_delete, create_time, create_by, update_time, update_by, display_order, is_able_pick from post_sale_express_company
    </sql>

    <select id="selectPostSaleExpressCompanyList" parameterType="PostSaleExpressCompany" resultMap="PostSaleExpressCompanyResult">
        <include refid="selectPostSaleExpressCompanyVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="displayOrder != null "> and display_order = #{displayOrder}</if>
            <if test="isAblePick != null "> and is_able_pick = #{isAblePick}</if>
        </where>
        order by display_order asc
    </select>
        
    <insert id="insertPostSaleExpressCompany" parameterType="PostSaleExpressCompany" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_express_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="displayOrder != null">display_order,</if>
            <if test="isAblePick != null">is_able_pick,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
            <if test="isAblePick != null">#{isAblePick},</if>
         </trim>
    </insert>

    <update id="updatePostSaleExpressCompany" parameterType="PostSaleExpressCompany">
        update post_sale_express_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
            <if test="isAblePick != null">is_able_pick = #{isAblePick},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleExpressCompanyByIds" parameterType="String">
        update post_sale_express_company set is_delete = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>