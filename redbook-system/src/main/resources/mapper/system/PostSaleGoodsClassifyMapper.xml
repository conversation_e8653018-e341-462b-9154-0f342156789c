<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleGoodsClassifyMapper">
    
    <resultMap type="PostSaleGoodsClassify" id="PostSaleGoodsClassifyResult">
        <result property="id"    column="id"    />
        <result property="classifyName"    column="classify_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="storeNum"    column="store_num"    />
    </resultMap>

    <sql id="selectPostSaleGoodsClassifyVo">
        select id, classify_name, create_time, create_by, update_time, update_by, is_delete, store_num from post_sale_goods_classify
    </sql>

    <select id="selectPostSaleGoodsClassifyList" parameterType="PostSaleGoodsClassify" resultMap="PostSaleGoodsClassifyResult">
        <include refid="selectPostSaleGoodsClassifyVo"/>
        <where>
            is_delete=0
            <if test="classifyName != null  and classifyName != ''"> and classify_name like concat('%', #{classifyName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="storeNum != null "> and store_num = #{storeNum}</if>
        </where>
    </select>
    
    <select id="selectPostSaleGoodsClassifyById" parameterType="Long" resultMap="PostSaleGoodsClassifyResult">
        <include refid="selectPostSaleGoodsClassifyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleGoodsClassify" parameterType="PostSaleGoodsClassify" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_goods_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="storeNum != null">store_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">#{classifyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="storeNum != null">#{storeNum},</if>
         </trim>
    </insert>

    <update id="updatePostSaleGoodsClassify" parameterType="PostSaleGoodsClassify">
        update post_sale_goods_classify
        <trim prefix="SET" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name = #{classifyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="storeNum != null">store_num = #{storeNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleGoodsClassifyById" parameterType="Long">
        update post_sale_goods_classify set is_delete = 1,update_by = #{userId} where id = #{id}
    </delete>

    <delete id="deletePostSaleGoodsClassifyByIds" parameterType="String">
        update post_sale_goods_classify set is_delete = 1,update_by = #{userId} where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>