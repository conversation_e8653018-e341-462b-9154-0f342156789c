<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleSparePartMapper">
    
    <resultMap type="PostSaleSparePart" id="PostSaleSparePartResult">
        <result property="id"    column="id"    />
        <result property="sparePartCode"    column="spare_part_code"    />
        <result property="sparePartName"    column="spare_part_name"    />
        <result property="sparePartDesc"    column="spare_part_desc"    />
        <result property="modelId"    column="model_id"    />
        <result property="isShow"    column="is_show"    />
        <result property="remarks"    column="remarks"    />
        <result property="sparePartStoreNum"    column="spare_part_store_num"    />
        <result property="sparePartCost"    column="spare_part_cost"    />
        <result property="sparePartTotalCost"    column="spare_part_total_cost"    />
        <result property="sparePartSalePrice"    column="spare_part_sale_price"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="supplyName"    column="supply_name"    />
        <result property="modelName"    column="model_name"    />
    </resultMap>

    <sql id="selectPostSaleSparePartVo">
        select pssp.id, pssp.spare_part_code, pssp.spare_part_name,
               pssp.spare_part_desc, pssp.model_id, pssp.is_show, pssp.remarks,
               pssp.spare_part_store_num, pssp.spare_part_cost, pssp.spare_part_total_cost,
               pssp.spare_part_sale_price,rtm.name as model_name, rts.supply_name
        from post_sale_spare_part pssp
         left join  redbook_tablet_model rtm on pssp.model_id=rtm.id
         left join  redbook_tablet_supply rts on rtm.supply_id= rts.id
    </sql>

    <select id="selectPostSaleSparePartList" parameterType="PostSaleSparePart" resultMap="PostSaleSparePartResult">
        <include refid="selectPostSaleSparePartVo"/>
        <where>
            <if test="modelId != null  and modelId != ''"> and rtm.id = #{modelId}</if>
            <if test="supplyId != null  and supplyId != ''"> and rts.id = #{supplyId}</if>
            <if test="sparePartCode != null  and sparePartCode != ''"> and pssp.spare_part_code like concat('%', #{sparePartCode}, '%')</if>
            <if test="sparePartName != null  and sparePartName != ''"> and pssp.spare_part_name like concat('%', #{sparePartName}, '%')</if>
            <if test="sparePartDesc != null  and sparePartDesc != ''"> and pssp.spare_part_desc like concat('%', #{sparePartDesc}, '%')</if>
            <if test="modelName != null  and modelName != ''"> and rtm.name like concat('%', #{modelName}, '%')</if>
            <if test="params!=null and params.createTimeS !=null">and psgio.create_time &gt;= #{params.createTimeS}' 00:00:00' </if>
            <if test="params!=null and params.createTimeE !=null">and psgio.create_time &lt;= #{params.createTimeE}' 23:59:59' </if>
        </where>
    </select>
    
    <select id="selectPostSaleSparePartById" parameterType="integer" resultMap="PostSaleSparePartResult">
        <include refid="selectPostSaleSparePartVo"/>
        where pssp.id = #{id}
    </select>
    <select id="selectPostSaleSparePartListFromApplet"
            resultType="com.redbook.system.domain.PostSaleSparePart">
        select pssp.spare_part_code as sparePartCode, pssp.spare_part_name as sparePartName,
               pssp.spare_part_desc as sparePartDesc,
               pssp.spare_part_sale_price as sparePartSalePrice
        from post_sale_spare_part pssp
        where is_show=1
    </select>

    <insert id="insertPostSaleSparePart" parameterType="PostSaleSparePart" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_spare_part
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sparePartCode != null">spare_part_code,</if>
            <if test="sparePartName != null">spare_part_name,</if>
            <if test="sparePartDesc != null">spare_part_desc,</if>
            <if test="modelId != null">model_id,</if>
            <if test="isShow != null">is_show,</if>
            <if test="remarks != null">remarks,</if>
            <if test="sparePartStoreNum != null">spare_part_store_num,</if>
            <if test="sparePartCost != null">spare_part_cost,</if>
            <if test="sparePartTotalCost != null">spare_part_total_cost,</if>
            <if test="sparePartSalePrice != null">spare_part_sale_price,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sparePartCode != null">#{sparePartCode},</if>
            <if test="sparePartName != null">#{sparePartName},</if>
            <if test="sparePartDesc != null">#{sparePartDesc},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="sparePartStoreNum != null">#{sparePartStoreNum},</if>
            <if test="sparePartCost != null">#{sparePartCost},</if>
            <if test="sparePartTotalCost != null">#{sparePartTotalCost},</if>
            <if test="sparePartSalePrice != null">#{sparePartSalePrice},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePostSaleSparePart" parameterType="PostSaleSparePart">
        update post_sale_spare_part
        <trim prefix="SET" suffixOverrides=",">
            <if test="sparePartCode != null">spare_part_code = #{sparePartCode},</if>
            <if test="sparePartName != null">spare_part_name = #{sparePartName},</if>
            <if test="sparePartDesc != null">spare_part_desc = #{sparePartDesc},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="sparePartStoreNum != null">spare_part_store_num = #{sparePartStoreNum},</if>
            <if test="sparePartCost != null">spare_part_cost = #{sparePartCost},</if>
            <if test="sparePartTotalCost != null">spare_part_total_cost = #{sparePartTotalCost},</if>
            <if test="sparePartSalePrice != null">spare_part_sale_price = #{sparePartSalePrice},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleSparePartById" parameterType="Long">
        delete from post_sale_spare_part where id = #{id}
    </delete>

    <delete id="deletePostSaleSparePartByIds" parameterType="String">
        delete from post_sale_spare_part where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="deletePostSaleSparePartStoreNum">
        update post_sale_spare_part
        set spare_part_store_num = CASE
        WHEN spare_part_store_num - #{num}  &lt; 0 THEN 0
        ELSE spare_part_store_num - #{num}
        END
        where id = #{id}
    </update>
</mapper>