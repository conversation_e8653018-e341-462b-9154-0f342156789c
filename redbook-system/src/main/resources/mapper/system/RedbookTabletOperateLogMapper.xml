<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RedbookTabletOperateLogMapper">
    
    <resultMap type="RedbookTabletOperateLog" id="RedbookTabletOperateLogResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="SN"    />
        <result property="operateType"    column="operate_type"    />
        <result property="operateDesc"    column="operate_desc"    />
        <result property="reason"    column="reason"    />
        <result property="operateUser"    column="operate_user"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRedbookTabletOperateLogVo">
        select l.id, l.SN, l.operate_type, l.operate_desc, l.reason, l.operate_user, l.create_time ,d.nick_name as operateUserName
        from redbook_tablet_operate_log l
                 left join sys_user d on l.operate_user = d.user_name
    </sql>

    <select id="selectRedbookTabletOperateLogList" parameterType="RedbookTabletOperateLog" resultMap="RedbookTabletOperateLogResult">
        <include refid="selectRedbookTabletOperateLogVo"/>
        <where>  
            <if test="sn != null  and sn != ''"> and l.SN = #{sn}</if>
            <if test="operateType != null  and operateType != ''"> and l.operate_type = #{operateType}</if>
            <if test="operateDesc != null  and operateDesc != ''"> and l.operate_desc = #{operateDesc}</if>
            <if test="reason != null  and reason != ''"> and l.reason = #{reason}</if>
            <if test="operateUser != null  and operateUser != ''"> and l.operate_user = #{operateUser}</if>
        </where>
        order by l.create_time desc
    </select>
    
    <select id="selectRedbookTabletOperateLogById" parameterType="Long" resultMap="RedbookTabletOperateLogResult">
        <include refid="selectRedbookTabletOperateLogVo"/>
        where l.id = #{id}
    </select>
        
    <insert id="insertRedbookTabletOperateLog" parameterType="RedbookTabletOperateLog" useGeneratedKeys="true" keyProperty="id">
        insert into redbook_tablet_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sn != null and sn != ''">SN,</if>
            <if test="operateType != null and operateType != ''">operate_type,</if>
            <if test="operateDesc != null">operate_desc,</if>
            <if test="reason != null">reason,</if>
            <if test="operateUser != null and operateUser != ''">operate_user,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="operateType != null and operateType != ''">#{operateType},</if>
            <if test="operateDesc != null">#{operateDesc},</if>
            <if test="reason != null">#{reason},</if>
            <if test="operateUser != null and operateUser != ''">#{operateUser},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRedbookTabletOperateLog" parameterType="RedbookTabletOperateLog">
        update redbook_tablet_operate_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="sn != null and sn != ''">SN = #{sn},</if>
            <if test="operateType != null and operateType != ''">operate_type = #{operateType},</if>
            <if test="operateDesc != null">operate_desc = #{operateDesc},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="operateUser != null and operateUser != ''">operate_user = #{operateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRedbookTabletOperateLogById" parameterType="Long">
        delete from redbook_tablet_operate_log where id = #{id}
    </delete>

    <delete id="deleteRedbookTabletOperateLogByIds" parameterType="String">
        delete from redbook_tablet_operate_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>