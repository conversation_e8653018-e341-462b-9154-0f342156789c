<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleFaultClassifyMapper">
    
    <resultMap type="PostSaleFaultClassify" id="PostSaleFaultClassifyResult">
        <result property="id"    column="id"    />
        <result property="classifyName"    column="classify_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="level"    column="level"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="faultDesc"    column="fault_desc"    />
        <result property="faultNum"    column="fault_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPostSaleFaultClassifyVo">
        select id, classify_name, parent_id, level,sort_order, fault_desc, fault_num, create_time, create_by, update_time, update_by, is_delete from post_sale_fault_classify
    </sql>

    <select id="selectPostSaleFaultClassifyList" parameterType="PostSaleFaultClassify" resultMap="PostSaleFaultClassifyResult">
        <include refid="selectPostSaleFaultClassifyVo"/>
        <where>
            <if test="classifyName != null  and classifyName != ''"> and classify_name like concat('%', #{classifyName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="level != null "> and sort_order = #{level}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="faultDesc != null  and faultDesc != ''"> and fault_desc = #{faultDesc}</if>
            <if test="faultNum != null "> and fault_num = #{faultNum}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectPostSaleFaultClassifyById"  resultMap="PostSaleFaultClassifyResult">
        <include refid="selectPostSaleFaultClassifyVo"/>
        where id = #{id}
    </select>
    <select id="selectChildListByParentId" resultType="java.lang.Long">
        select id from post_sale_fault_classify where parent_id = #{parentId} and is_delete=0
    </select>
    <select id="selectListByIds" resultType="com.redbook.system.domain.PostSaleFaultClassify">
        select psfc1.id, psfc1.classify_name,psfc2.id,psfc2.classify_name from post_sale_fault_classify psfc1
                 left join post_sale_fault_classify psfc2 on psfc1.parent_id=psfc2.id
                 where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectAllFaultList" resultMap="PostSaleFaultClassifyResult">
        select id, classify_name from post_sale_fault_classify
    </select>

    <insert id="insertPostSaleFaultClassify" parameterType="PostSaleFaultClassify" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_fault_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="level != null">level,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="faultDesc != null">fault_desc,</if>
            <if test="faultNum != null">fault_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">#{classifyName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="level != null">#{level},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="faultDesc != null">#{faultDesc},</if>
            <if test="faultNum != null">#{faultNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updatePostSaleFaultClassify" parameterType="PostSaleFaultClassify">
        update post_sale_fault_classify
        <trim prefix="SET" suffixOverrides=",">
            <if test="classifyName != null and classifyName != ''">classify_name = #{classifyName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="level != null">sort_order = #{level},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="faultDesc != null">fault_desc = #{faultDesc},</if>
            <if test="faultNum != null">fault_num = #{faultNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleFaultClassifyById" parameterType="Long">
        update post_sale_fault_classify set is_delete=1,update_by=#{userId} where id = #{id}
    </delete>

    <delete id="deletePostSaleFaultClassifyByIds" parameterType="String">
        update  post_sale_fault_classify set is_delete=1,update_by=#{userId} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>