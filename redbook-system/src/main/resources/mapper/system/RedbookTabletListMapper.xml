<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RedbookTabletListMapper">
    <resultMap type="RedbookTabletList" id="TabletListDetailResult">
        <result property="id" column="Id"/>
        <result property="batchId" column="Batch_Id"/>
        <result property="boxNum" column="Box_Num"/>
        <result property="sn" column="SN"/>
        <result property="imei1" column="IMEI_1"/>
        <result property="imei2" column="IMEI_2"/>
        <result property="mac" column="MAC"/>
        <result property="bt" column="BT"/>
        <result property="mfd" column="MFD"/>
        <result property="aid" column="aid"/>
        <result property="stockStatus" column="stock_status"/>
        <result property="isStandby" column="is_standby"/>
        <result property="activationDate" column="activation_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="lastUserId" column="last_user_id"/>
        <result property="lastUserName" column="last_user_name"/>
        <result property="saleDate" column="sale_date"/>
        <result property="stockDate" column="stock_date"/>
        <result property="orderNo" column="order_no"/>
        <result property="batchName" column="batchName"/>
        <result property="modelName" column="modelName"/>
        <result property="supplyName" column="supplyName"/>
        <result property="agentName" column="agentName"/>
        <result property="agentId" column="agentId"/>
        <result property="WIFI" column="WIFI"/>
        <result property="simNumber" column="sim_number"/>
        <result property="keyboardNumber" column="keyboard_number"/>
        <result property="status" column="status"/>
        <result property="lastLocation" column="last_location"/>
        <result property="lastLocationStr" column="last_location_str"/>
        <result property="adapterId" column="adapter_id"/>
        <result property="networkLicense" column="network_license"/>
        <result property="deliveryRemark" column="delivery_remark"/>
        <result property="workLogFlag" column="work_log_flag"/>
    </resultMap>
    <resultMap type="StockTabletListVo" id="StockTabletListResult">
        <result property="id" column="Id"/>
        <result property="supplyName" column="supplyName"/>
        <result property="modelName" column="modelName"/>
        <result property="batchName" column="batchName"/>
        <result property="sn" column="SN"/>
        <result property="activationStatus" column="activationStatus"/>
        <result property="status" column="status"/>
        <result property="activationDate" column="activation_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="lastUserId" column="last_user_id"/>
        <result property="lastUserName" column="last_user_name"/>
    </resultMap>

    <resultMap type="AgentTabletListVo" id="AgentTabletListResult">
        <result property="id" column="Id"/>
        <result property="supplyName" column="supplyName"/>
        <result property="modelName" column="modelName"/>
        <result property="batchName" column="batchName"/>
        <result property="boxNum" column="Box_Num"/>
        <result property="sn" column="SN"/>
        <result property="saleStatus" column="saleStatus"/>
        <result property="status" column="status"/>
        <result property="saleDate" column="sale_date"/>
        <result property="activationStatus" column="activationStatus"/>
        <result property="activationDate" column="activation_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="agentName" column="agentName"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="exclusiveShopName" column="exclusive_shop_name"/>
        <result property="lastUserId" column="last_user_id"/>
        <result property="lastUserName" column="last_user_name"/>
        <result property="adapterId" column="adapter_id"/>
        <result property="networkLicense" column="network_license"/>
        <result property="deliveryRemark" column="delivery_remark"/>
        <result property="workLogFlag" column="work_log_flag"/>
    </resultMap>
    <sql id="selectTabletDetailInfoVo">
        select b.Batch_Name                                                        as batchName,
               c.`name`                                                            as modelName,
               d.supply_name                                                       as supplyName,
               a.Id,
               a.Batch_Id,
               a.Box_Num,
               a.SN,
               a.IMEI_1,
               a.IMEI_2,
               a.MAC,
               a.BT,
               a.MFD,
               a.aid,
               a.stock_status,
               a.is_standby,
               a.activation_date,
               a.expiration_date,
               a.last_user_id                                            last_user_id,
               stu.user_name last_user_name,
               a.sale_date,
               a.stock_date,
               a.order_no,
               a.WIFI,
               a.sim_number,
               a.keyboard_number,
               a.status,
               a.last_location,
               a.last_location_str,
               case when a.sale_date is null then '未售' else '已售' end           as saleStatus,
               case when a.activation_date is null then '未激活' else '已激活' end as activationStatus,
               f.name                                                              as agentName,
               f.id                                                                as agentId,
               a.exclusive_shop_id,
               a.adapter_id,
               a.network_license,
               a.delivery_remark,
               a.work_log_flag,
               es.name exclusive_shop_name
        from redbook_tablet_list a
                 left join redbook_tablet_batch b on a.Batch_Id = b.id
                 LEFT JOIN hssword_red_book_management.redbook_tablet_model c on b.model_id = c.id
                 left JOIN hssword_red_book_management.redbook_tablet_supply d on c.supply_id = d.id
                 left join hssword_red_book_management.agent f on a.aid = f.aid
                 left join exclusive_shop es on a.exclusive_shop_id=es.id
                 left join `hssword_red_book`.`user_info` stu on a.last_user_id=stu.user_id
    </sql>

    <sql id="selectStockTabletListVo">
        select b.Batch_Name                                                        as batchName,
               c.`name`                                                            as modelName,
               d.supply_name                                                       as supplyName,
               a.Id,
               a.SN,
               a.status,
               a.activation_date,
               a.expiration_date,
               a.exclusive_shop_id,
               es.name exclusive_shop_name,
               a.last_user_id                                            last_user_id,
               a.work_log_flag,
               stu.user_name last_user_name,
               case when a.activation_date is null then '未激活' else '已激活' end as activationStatus
        from redbook_tablet_list a
                 left join redbook_tablet_batch b on a.Batch_Id = b.id
                 LEFT JOIN hssword_red_book_management.redbook_tablet_model c on b.model_id = c.id
                 left JOIN hssword_red_book_management.redbook_tablet_supply d on c.supply_id = d.id
                 left join exclusive_shop es on a.exclusive_shop_id=es.id
                 left join `hssword_red_book`.`user_info` stu on a.last_user_id=stu.user_id
    </sql>
    <select id="selectStockTabletList" parameterType="StockTabletListDto" resultMap="StockTabletListResult">
        <include refid="selectStockTabletListVo"/>
        <where>
            a.aid is null
            <if test="supplyId != null  and supplyId > 0">
                and d.id = #{supplyId}
            </if>
            <if test="modelId != null  and modelId > 0">
                and c.id = #{modelId}
            </if>
            <if test="batchId != null and batchId > 0">
                and a.Batch_Id = #{batchId}
            </if>
            <if test="sn != null  and sn != ''">
                and a.SN = #{sn}
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="activationStatus != null and activationStatus == 0">
                and a.activation_date is null
            </if>
            <if test="activationStatus != null and activationStatus == 1">
                and a.activation_date is not null
            </if>
            <if test="boxNum != null and boxNum != ''">
                and a.Box_Num = #{boxNum}
            </if>
        </where>
        order by a.work_log_flag desc, a.id desc
    </select>


    <sql id="selectAgentTabletListVo">
        select b.Batch_Name                                                        as batchName,
               c.code                                                            as modelName,
               d.supply_name                                                       as supplyName,
               a.Box_Num,
               a.Id,
               a.SN,
               a.status,
               case when a.sale_date is null then '未售' else '已售' end           as saleStatus,
               a.sale_date,
               a.activation_date,
               case when a.activation_date is null then '未激活' else '已激活' end as activationStatus,
               a.expiration_date,
               f.name                                                              as agentName,
               f.id                                                                as agentId,
               a.exclusive_shop_id,
               es.name exclusive_shop_name,
               a.last_user_id                                            last_user_id,
                a.work_log_flag,
                stu.user_name last_user_name
        from redbook_tablet_list a
                 left join redbook_tablet_batch b on a.Batch_Id = b.id
                 LEFT JOIN hssword_red_book_management.redbook_tablet_model c on b.model_id = c.id
                 left JOIN hssword_red_book_management.redbook_tablet_supply d on c.supply_id = d.id
                 left join hssword_red_book_management.agent f on a.aid = f.aid
                <if test="mId != null and agentIdList.size==0">
                    LEFT JOIN agent_mapping am ON f.id = am.agent_id and am.user_id = #{mId}
                </if>
                 left join sys_user u2 ON f.contact_person = u2.user_id
                 left join exclusive_shop es on a.exclusive_shop_id=es.id
                left join `hssword_red_book`.`user_info` stu on a.last_user_id=stu.user_id
    </sql>

    <select id="selectAgentTabletList" parameterType="AgentTabletListDto" resultMap="AgentTabletListResult">
        <include refid="selectAgentTabletListVo"/>
        <where>
            <choose>
                <when test="aid != null and aid != ''">
                    and a.aid = #{aid}
                </when>
                <otherwise>
                    and a.aid is not null
                </otherwise>
            </choose>
            <if test="saleStatus != null and saleStatus == 0">
                and sale_date is null
            </if>
            <if test="saleStatus != null and saleStatus == 1">
                and sale_date is not null
            </if>
            <if test="activationStatus != null and activationStatus == 0">
                and activation_date is null
            </if>
            <if test="activationStatus != null and activationStatus == 1">
                and activation_date is not null
            </if>
            <if test="batchId != null">
                and Batch_Id = #{batchId}
            </if>
            <if test="boxNum != null  and boxNum != ''">
                and Box_Num = #{boxNum}
            </if>
            <if test="sn != null  and sn != ''">
                and SN = #{sn}
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="saleDate != null">
                and sale_date &gt;= #{saleDate}
            </if>
            <if test="saleDateEnd != null">
                and sale_date &lt;= #{saleDateEnd}
            </if>
            <if test="activationDate != null">
                and activation_date &gt;= #{activationDate}
            </if>
            <if test="activationDateEnd != null">
                and activation_date &lt;= #{activationDateEnd}
            </if>
            <if test="modelId != null">
                and c.id = #{modelId}
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and a.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and a.exclusive_shop_id is null
            </if>
            <if test="agentIdList.size>0">
                and f.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="params != null and params.supplyId !=null ">
                and d.id = #{params.supplyId}
            </if>
        </where>
        order by a.work_log_flag desc, a.id desc
    </select>
    <select id="selectRedbookTabletListById" parameterType="Long" resultMap="TabletListDetailResult">
        <include refid="selectTabletDetailInfoVo"/>
        where a.Id = #{id}
    </select>

    <insert id="insertRedbookTabletList" parameterType="RedbookTabletList" useGeneratedKeys="true" keyProperty="id">
        insert into redbook_tablet_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchId != null">
                Batch_Id,
            </if>
            <if test="boxNum != null and boxNum != ''">
                Box_Num,
            </if>
            <if test="sn != null and sn != ''">
                SN,
            </if>
            <if test="imei1 != null and imei1 != ''">
                IMEI_1,
            </if>
            <if test="imei2 != null and imei2 != ''">
                IMEI_2,
            </if>
            <if test="mac != null">
                MAC,
            </if>
            <if test="bt != null">
                BT,
            </if>
            <if test="mfd != null">
                MFD,
            </if>
            <if test="aid != null">
                aid,
            </if>
            <if test="stockStatus != null">
                stock_status,
            </if>
            <if test="isStandby != null">
                is_standby,
            </if>
            <if test="activationDate != null">
                activation_date,
            </if>
            <if test="expirationDate != null">
                expiration_date,
            </if>
            <if test="lastUserId != null">
                last_user_id,
            </if>
            <if test="saleDate != null">
                sale_date,
            </if>
            <if test="stockDate != null">
                stock_date,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="WIFI != null and WIFI != ''">
                WIFI,
            </if>
            <if test="simNumber != null and simNumber != ''">
                sim_number,
            </if>
            <if test="keyboardNumber != null and keyboardNumber != ''">
                keyboard_number,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="lastLocation != null and lastLocation != ''">
                last_location,
            </if>
            <if test="lastLocationStr != null and lastLocationStr != ''">
                last_location_str,
            </if>
            <if test="adapterId != null and adapterId != ''">
                adapter_id,
            </if>
            <if test="networkLicense != null and networkLicense != ''">
                network_license,
            </if>
            <if test="deliveryRemark != null and deliveryRemark != ''">
                delivery_remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchId != null">
                #{batchId},
            </if>
            <if test="boxNum != null and boxNum != ''">
                #{boxNum},
            </if>
            <if test="sn != null and sn != ''">
                #{sn},
            </if>
            <if test="imei1 != null and imei1 != ''">
                #{imei1},
            </if>
            <if test="imei2 != null and imei2 != ''">
                #{imei2},
            </if>
            <if test="mac != null">
                #{mac},
            </if>
            <if test="bt != null">
                #{bt},
            </if>
            <if test="mfd != null">
                #{mfd},
            </if>
            <if test="aid != null">
                #{aid},
            </if>
            <if test="stockStatus != null">
                #{stockStatus},
            </if>
            <if test="isStandby != null">
                #{isStandby},
            </if>
            <if test="activationDate != null">
                #{activationDate},
            </if>
            <if test="expirationDate != null">
                #{expirationDate},
            </if>
            <if test="lastUserId != null">
                #{lastUserId},
            </if>
            <if test="saleDate != null">
                #{saleDate},
            </if>
            <if test="stockDate != null">
                #{stockDate},
            </if>
            <if test="orderNo != null">
                #{orderNo},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="WIFI != null and WIFI != ''">
                #{WIFI},
            </if>
            <if test="simNumber != null and simNumber != ''">
                #{simNumber},
            </if>
            <if test="keyboardNumber != null and keyboardNumber != ''">
                #{keyboardNumber},
            </if>
            <if test="status!=null ">
                #{status},
            </if>
            <if test="lastLocation != null and lastLocation != ''">
                #{lastLocation},
            </if>
            <if test="lastLocationStr != null and lastLocationStr != ''">
                #{lastLocationStr},
            </if>
            <if test="adapterId != null and adapterId != ''">
                #{adapterId},
            </if>
            <if test="networkLicense != null and networkLicense != ''">
                #{networkLicense},
            </if>
            <if test="deliveryRemark != null and deliveryRemark != ''">
                #{deliveryRemark},
            </if>
        </trim>
    </insert>

    <!--<insert id="batchInsert" parameterType="java.util.List">
        insert into redbook_tablet_list (
        Batch_Id, Box_Num,SN,IMEI_1,IMEI_2,MAC,BT,MFD create_by,create_time)
        values
        <foreach collection="lists" item="item" separator=",">
            (#{item.batchId}, #{item.boxNum},
            #{item.sn}, #{item.imei1}, #{item.imei2}, #{item.mac}, #{item.bt},
            #{item.mfd},#{item.updateBy},#{item.createTime})
        </foreach>
    </insert>-->
    <update id="updateRedbookTabletList" parameterType="RedbookTabletList">
        update redbook_tablet_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchId != null">
                Batch_Id = #{batchId},
            </if>
            <if test="boxNum != null and boxNum != ''">
                Box_Num = #{boxNum},
            </if>
            <if test="imei1 != null and imei1 != ''">
                IMEI_1 = #{imei1},
            </if>
            <if test="imei2 != null and imei2 != ''">
                IMEI_2 = #{imei2},
            </if>
            <if test="mac != null">
                MAC = #{mac},
            </if>
            <if test="bt != null">
                BT = #{bt},
            </if>
            <if test="mfd != null">
                MFD = #{mfd},
            </if>
            <if test="aid != null">
                aid = #{aid},
            </if>
            <if test="stockStatus != null">
                stock_status = #{stockStatus},
            </if>
            <if test="isStandby != null">
                is_standby = #{isStandby},
            </if>
            <if test="activationDate != null">
                activation_date = #{activationDate},
            </if>
            <if test="expirationDate != null">
                expiration_date = #{expirationDate},
            </if>
            <if test="lastUserId != null">
                last_user_id = #{lastUserId},
            </if>
            <if test="saleDate != null">
                sale_date = #{saleDate},
            </if>
            <if test="stockDate != null">
                stock_date = #{stockDate},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="WIFI != null and WIFI != ''">
                WIFI=#{WIFI},
            </if>
            <if test="simNumber != null and simNumber != ''">
                sim_number=#{simNumber},
            </if>
            <if test="keyboardNumber != null and keyboardNumber != ''">
                keyboard_number=#{keyboardNumber},
            </if>
            <if test="adapterId != null and adapterId != ''">
                adapter_id = #{adapterId},
            </if>
            <if test="networkLicense != null and networkLicense != ''">
                network_license = #{networkLicense},
            </if>
            <if test="deliveryRemark != null and deliveryRemark != ''">
                delivery_remark = #{deliveryRemark},
            </if>
            <if test="workLogFlag != null and workLogFlag == 1">
                work_log_flag = 1,
            </if>
            <if test="workLogFlag != null and workLogFlag == 2">
                work_log_flag = 0,
            </if>
        </trim>
        where SN = #{sn}
    </update>
    <update id="batchUpdate">
        update redbook_tablet_list
        set stock_status = #{stockStatus},
            update_by=#{username},
            update_time  =now(),
            aid=#{aid},
            exclusive_shop_id=#{exclusiveShopId}
        where Id in
        <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="cleanUsageTraces">
        update redbook_tablet_list
        set
            update_by=#{username},
            update_time  =now(),
            activation_date=NULL,
            expiration_date=NULL,
            last_user_id=NULL,
            sale_date=NULL
        where SN=#{sn}
    </update>
    <delete id="cleanTabletLogonRecord">
        delete from hssword_red_book.user_logon_record where SN=#{sn}
    </delete>

    <delete id="deleteRedbookTabletListById" parameterType="Long">
        delete
        from redbook_tablet_list
        where Id = #{id}
    </delete>

    <delete id="deleteRedbookTabletListByIds" parameterType="String">
        delete
        from redbook_tablet_list where Id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRedbookTabletListBySN" resultMap="TabletListDetailResult">
        <include refid="selectTabletDetailInfoVo"/>
        where a.SN = #{sn}
    </select>

    <select id="getTabletLogonRecordList" resultType="map">
        select u.user_name,
                ul.user_id,
                ul.ip,
                ul.section,
                ul.logon_time,
                ul.version_name
        from ${DB_RED_BOOK}.user_logon_record ul
        left join ${DB_RED_BOOK}.user_info u on u.user_id = ul.user_id
        where ul.SN = #{sn}
        <if test="limitStartTime != null and limitStartTime != ''">
            and ul.logon_time &gt;=#{limitStartTime}
            and ul.logon_time &lt;=#{limitEndTime}
        </if>
        ORDER BY ul.logon_time DESC
        <if test="pageIndex != null and pageIndex != -1 and pageSize != null and pageSize != -1">
            LIMIT ${pageIndex},${pageSize}
        </if>
    </select>
    <select id="getTabletLogonRecordCount" resultType="integer">
        select count(ul.id)
        from ${DB_RED_BOOK}.user_logon_record ul
        where ul.SN = #{sn}
        <if test="limitStartTime != null and limitStartTime != ''">
            and ul.logon_time &gt;=#{limitStartTime}
            and ul.logon_time &lt;=#{limitEndTime}
        </if>
    </select>
</mapper>