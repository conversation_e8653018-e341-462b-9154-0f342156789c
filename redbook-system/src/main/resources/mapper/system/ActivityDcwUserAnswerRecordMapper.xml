<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityDcwUserAnswerRecordMapper">
    <resultMap id="BaseResultMap" type="com.redbook.system.domain.ActivityDcwUserAnswerRecord">

        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="activity_base_id" jdbcType="INTEGER" property="activityBaseId"/>
        <result column="activity_content_id" jdbcType="INTEGER" property="activityContentId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="stage" jdbcType="INTEGER" property="stage"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_addr" jdbcType="VARCHAR" property="userAddr"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="score" jdbcType="INTEGER" property="score"/>
        <result column="score_efficiency" jdbcType="DOUBLE" property="scoreEfficiency"/>
        <result column="game_stage" jdbcType="INTEGER" property="gameStage"/>
        <result column="create_date" jdbcType="DATE" property="createDate"/>
        <result column="game_type" jdbcType="INTEGER" property="gameType"/>
        <result column="aid" jdbcType="VARCHAR" property="aid"/>
    </resultMap>
    <sql id="Base_Column_List">

        id
        , activity_base_id, activity_content_id, user_id, stage, user_name, user_addr,
    create_time, update_time, score, game_stage, create_date, game_type,aid,score_efficiency
    </sql>
    <select id="selectUserScoreList" resultMap="BaseResultMap">
        select * from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_user_answer_record where activity_base_id=#{activityBaseId} and user_id=#{userId} order by id asc
    </select>

</mapper>