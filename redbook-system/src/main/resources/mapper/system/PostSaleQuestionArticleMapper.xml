<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleQuestionArticleMapper">
    
    <resultMap type="PostSaleQuestionArticle" id="PostSaleQuestionArticleResult">
        <result property="id"    column="id"    />
        <result property="articleTitle"    column="article_title"    />
        <result property="questionClassifyId"    column="question_classify_id"    />
        <result property="articleContent"    column="article_content"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPostSaleQuestionArticleVo">
        select id, article_title, question_classify_id, article_content, create_time, create_by, update_time, update_by, is_delete from post_sale_question_article
    </sql>

    <select id="selectPostSaleQuestionArticleList" parameterType="PostSaleQuestionArticle" resultMap="PostSaleQuestionArticleResult">
        <include refid="selectPostSaleQuestionArticleVo"/>
        <where>
            is_delete=0
            <if test="articleTitle != null  and articleTitle != ''"> and article_title = #{articleTitle}</if>
            <if test="questionClassifyId != null "> and question_classify_id = #{questionClassifyId}</if>
            <if test="articleContent != null  and articleContent != ''"> and article_content = #{articleContent}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
    </select>
    
    <select id="selectPostSaleQuestionArticleById" parameterType="Long" resultMap="PostSaleQuestionArticleResult">
        <include refid="selectPostSaleQuestionArticleVo"/>
        where id = #{id}
    </select>
    <select id="selectPostSaleQuestionArticleListFromApplet"
            resultType="com.redbook.system.domain.PostSaleQuestionArticle">
        select id, article_title as articleTitle from post_sale_question_article
        where is_delete=0 order by id asc
        limit #{offset},#{pageSize}
    </select>

    <insert id="insertPostSaleQuestionArticle" parameterType="PostSaleQuestionArticle" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_question_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="articleTitle != null">article_title,</if>
            <if test="questionClassifyId != null">question_classify_id,</if>
            <if test="articleContent != null">article_content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="articleTitle != null">#{articleTitle},</if>
            <if test="questionClassifyId != null">#{questionClassifyId},</if>
            <if test="articleContent != null">#{articleContent},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updatePostSaleQuestionArticle" parameterType="PostSaleQuestionArticle">
        update post_sale_question_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="articleTitle != null">article_title = #{articleTitle},</if>
            <if test="questionClassifyId != null">question_classify_id = #{questionClassifyId},</if>
            <if test="articleContent != null">article_content = #{articleContent},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleQuestionArticleById" parameterType="Long">
        delete from post_sale_question_article where id = #{id}
    </delete>

    <delete id="deletePostSaleQuestionArticleByIds" parameterType="String">
        delete from post_sale_question_article where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>