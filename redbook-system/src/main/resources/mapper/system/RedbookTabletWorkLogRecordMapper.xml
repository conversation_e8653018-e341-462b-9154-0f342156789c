<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.RedbookTabletWorkLogRecordMapper">

    <resultMap type="RedbookTabletWorkLogRecord" id="RedbookTabletWorkLogRecordResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="SN"    />
        <result property="status"    column="status"    />
        <result property="logType"    column="log_type"    />
        <result property="logOssLink"    column="log_oss_link"    />
        <result property="operateUser"    column="operate_user"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRedbookTabletWorkLogRecordVo">
        select id, SN, status, log_type, log_oss_link, operate_user, create_time from redbook_tablet_work_log_record
    </sql>

    <select id="selectRedbookTabletWorkLogRecordList" parameterType="RedbookTabletWorkLogRecord" resultMap="RedbookTabletWorkLogRecordResult">
        <include refid="selectRedbookTabletWorkLogRecordVo"/>
        <where>
            <if test="sn != null  and sn != ''"> and SN = #{sn}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="logType != null  and logType != ''"> and log_type = #{logType}</if>
            <if test="logOssLink != null  and logOssLink != ''"> and log_oss_link = #{logOssLink}</if>
            <if test="operateUser != null  and operateUser != ''"> and operate_user = #{operateUser}</if>
        </where>
    </select>

    <select id="selectRedbookTabletWorkLogRecordById" resultMap="RedbookTabletWorkLogRecordResult">
        <include refid="selectRedbookTabletWorkLogRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectBySn" resultMap="RedbookTabletWorkLogRecordResult">
        <include refid="selectRedbookTabletWorkLogRecordVo"/>
        where SN = #{sn}
    </select>

    <insert id="insertRedbookTabletWorkLogRecord" parameterType="RedbookTabletWorkLogRecord" useGeneratedKeys="true" keyProperty="id">
        insert into redbook_tablet_work_log_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sn != null and sn != ''">SN,</if>
            <if test="status != null">status,</if>
            <if test="logType != null and logType != ''">log_type,</if>
            <if test="logOssLink != null">log_oss_link,</if>
            <if test="operateUser != null and operateUser != ''">operate_user,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="status != null">#{status},</if>
            <if test="logType != null and logType != ''">#{logType},</if>
            <if test="logOssLink != null">#{logOssLink},</if>
            <if test="operateUser != null and operateUser != ''">#{operateUser},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateRedbookTabletWorkLogRecord" parameterType="RedbookTabletWorkLogRecord">
        update redbook_tablet_work_log_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="sn != null and sn != ''">SN = #{sn},</if>
            <if test="status != null">status = #{status},</if>
            <if test="logType != null and logType != ''">log_type = #{logType},</if>
            <if test="logOssLink != null">log_oss_link = #{logOssLink},</if>
            <if test="operateUser != null and operateUser != ''">operate_user = #{operateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="update" parameterType="RedbookTabletWorkLogRecord">
        update redbook_tablet_work_log_record
        set status = #{status},
            log_type = #{logType},
            log_oss_link = #{logOssLink}
        where id = #{id}
    </update>

    <delete id="deleteRedbookTabletWorkLogRecordById">
        delete from redbook_tablet_work_log_record where id = #{id}
    </delete>

    <delete id="deleteRedbookTabletWorkLogRecordByIds" parameterType="String">
        delete from redbook_tablet_work_log_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>