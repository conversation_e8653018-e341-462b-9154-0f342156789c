package com.redbook.dto.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Author: qwy
 * @Date: 2019/3/28 16:29
 * @Version 1.0
 */
@ApiModel(reference = "AppletUserAuthorizeRequestDto")
public class AppletUserAuthorizeRequestDto {

    @ApiModelProperty(value = "微信密文 ", required = true)
    private String encryptedData;

    @ApiModelProperty(value = "微信用户所在端唯一标识", required = true)
    private String openId;

    @ApiModelProperty(value = "微信会话秘钥", required = true)
    private String sessionKey;

    @ApiModelProperty(value = "微信偏移量", required = true)
    private String iv;


    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData == null ? null : encryptedData.trim();
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId == null ? null : openId.trim();
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey == null ? null : sessionKey.trim();
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv == null ? null : iv.trim();
    }

}
