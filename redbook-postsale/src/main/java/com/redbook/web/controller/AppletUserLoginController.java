package com.redbook.web.controller;

import com.redbook.common.core.domain.AjaxResult;
import com.redbook.dto.login.AppletUserAuthorizeRequestDto;
import com.redbook.dto.login.AppletUserPhoneRequestDto;
import com.redbook.dto.login.AppletUserRequestDto;
import com.redbook.service.IAppletUserLoginService;
import com.redbook.vo.login.AppletUserAuthorizeResponseVO;
import com.redbook.vo.login.AppletUserResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/postSale/applet/userLogin/")
@Api(tags = "小程序-用户登录")
public class AppletUserLoginController {
    @Autowired
    IAppletUserLoginService appletUserLoginService;

    @ApiOperation(value = "查看是否授权过")
    @PostMapping("checkAuthorize")
    public AjaxResult<AppletUserResponseVO> checkAuthorize(@RequestBody @ApiParam(value = "请创建AppletUserRequestDto 对象进行传参", required = true) AppletUserRequestDto appletUserRequestDto) {
        return AjaxResult.success(appletUserLoginService.checkAuthorize(appletUserRequestDto));
    }

    @ApiOperation(value = "获取手机号")
    @PostMapping("getPhoneNumber")
    public AjaxResult<Boolean> getPhoneNumber(@RequestBody @ApiParam(value = "请创建AppletUserPhoneRequestDto 对象进行传参", required = true) AppletUserPhoneRequestDto appletUserPhoneRequestDto) {
        return AjaxResult.success(appletUserLoginService.getPhoneNumber(appletUserPhoneRequestDto));
    }

    @ApiOperation(value = "提交授权")
    @PostMapping("addAuthorize")
    public AjaxResult<AppletUserAuthorizeResponseVO> addAuthorize(@RequestBody @ApiParam(value = "请创建AppletUserAuthorizeRequestDto 对象进行传参", required = true) AppletUserAuthorizeRequestDto appletUserAuthorizeRequestVo) {
        return AjaxResult.success(appletUserLoginService.addAuthorize(appletUserAuthorizeRequestVo));
    }

    @ApiOperation(value = "刷新token")
    @PostMapping("refreshToken")
    public AjaxResult<AppletUserResponseVO> refreshToken(@ApiParam("微信id") @RequestParam("openId") String openId) {
        return AjaxResult.success(appletUserLoginService.refreshToken(openId));
    }
}
