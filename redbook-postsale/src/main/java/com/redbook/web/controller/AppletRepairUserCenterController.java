package com.redbook.web.controller;

import com.redbook.common.constant.PostSaleConstants;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.config.aop.RequestLimit;
import com.redbook.system.enums.PostSaleSmsBusinessEnum;
import com.redbook.system.service.postSale.IPostSaleRepairOrderService;
import com.redbook.system.service.postSale.IPostSaleUserCenterService;
import com.redbook.util.wehChat.JwtTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/postSale/applet/userCenter/")
@Api(tags = "小程序-用户中心")
public class AppletRepairUserCenterController extends BaseController {
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    HttpServletRequest request;

    @Autowired
    private IPostSaleUserCenterService postSaleUserCenterService;
    @GetMapping("/serviceConfig")
    @ApiOperation("服务配置")
    public AjaxResult serviceConfig() {
        return AjaxResult.success(postSaleUserCenterService.serviceConfig());
    }

    @PostMapping("/questionList")
    @ApiOperation("常见问题")
    public AjaxResult questionList(@ApiParam(value = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum, @ApiParam(value = "每页大小", required = false) @RequestParam(defaultValue = "10") Integer pageSize) {
        return AjaxResult.success(postSaleUserCenterService.questionList(pageNum, pageSize));
    }

    @GetMapping("/questionDetail/{quesitonId}")
    @ApiOperation("问题详情")
    public AjaxResult questionDetail(@PathVariable("quesitonId") Integer quesitonId) {
        return AjaxResult.success(postSaleUserCenterService.questionDetail(quesitonId));
    }

    @GetMapping("/sparePartList")
    @ApiOperation("零备件列表")
    public AjaxResult sparePartList() {
        return AjaxResult.success(postSaleUserCenterService.sparePartList());
    }

    @GetMapping("/sms")
    @ApiOperation("短信")
    public AjaxResult sms(@ApiParam(value = "手机号", required = true) @RequestParam String phone, @ApiParam(value = "短信业务类型", required = true) @RequestParam PostSaleSmsBusinessEnum postSaleSmsBusinessEnum) {
        return AjaxResult.success(postSaleUserCenterService.sms(phone, postSaleSmsBusinessEnum));
    }

    @GetMapping("/updatePhone")
    @ApiOperation("更换手机号")
    public AjaxResult updatePhone(@ApiParam(value = "手机号", required = true) @RequestParam String phone, @ApiParam(value = "验证码", required = true) @RequestParam String verificationCode) {
        return AjaxResult.success(postSaleUserCenterService.updatePhone(phone, verificationCode, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET));
    }
}
