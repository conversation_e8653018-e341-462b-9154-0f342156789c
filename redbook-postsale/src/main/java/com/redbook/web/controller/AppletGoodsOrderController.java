package com.redbook.web.controller;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.config.aop.RequestLimit;
import com.redbook.service.IPostSaleGoodsOrderService;
import com.redbook.system.domain.PostSaleGoodsOrder;
import com.redbook.util.wehChat.JwtTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 商品订单Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/applet/goodsOrder")
@Api(tags = "售后-商品订单")
public class AppletGoodsOrderController extends BaseController
{
    @Autowired
    private IPostSaleGoodsOrderService postSaleGoodsOrderService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    HttpServletRequest request;

    /**
     * 查询商品订单列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品订单列表")
    public TableDataInfo<PostSaleGoodsOrder> list(PostSaleGoodsOrder postSaleGoodsOrder)
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        startPage();
        postSaleGoodsOrder.setCreateBy(appletUserId);
        List<PostSaleGoodsOrder> list = postSaleGoodsOrderService.selectPostSaleGoodsOrderList(postSaleGoodsOrder);
        return getDataTable(list);
    }

    /**
     * 获取商品订单详细信息
     */
    @ApiOperation("获取商品订单详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(postSaleGoodsOrderService.selectPostSaleGoodsOrderById(id));
    }

    @ApiOperation("获取商品订单详细信息")
    @GetMapping(value = "/getOrder")
    public AjaxResult getInfo(@RequestParam("orderNo") String orderNo)
    {
        return success(postSaleGoodsOrderService.selectPostSaleGoodsOrderByOrderNo(orderNo));
    }

    /**
     * 再次购买
     */
    @ApiOperation("再次购买")
    @Log(title = "商品订单", businessType = BusinessType.INSERT)
    @PostMapping(value = "/buyAgain/{id}")
    @RequestLimit
    public AjaxResult buyAgain(@PathVariable("id") Long id)
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        return toAjax(postSaleGoodsOrderService.buyAgain(Integer.valueOf(appletUserId),id));
    }

    /**
     * 取消订单-退款
     */
    @ApiOperation("取消商品订单")
    @Log(title = "商品订单", businessType = BusinessType.CANCEL)
    @PostMapping("/cancelOrder/{id}")
    public AjaxResult cancel(@PathVariable("id") Long id)
    {
        return postSaleGoodsOrderService.cancelPostSaleGoodsOrder(id);
    }


}
