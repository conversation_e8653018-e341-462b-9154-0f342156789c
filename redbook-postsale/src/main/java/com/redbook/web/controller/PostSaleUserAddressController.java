package com.redbook.web.controller;

import java.util.List;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.service.IPostSaleUserAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.PostSaleUserAddress;

/**
 * 用户地址Controller
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/applet/userAddress")
@Api(tags = "售后-用户地址")
public class PostSaleUserAddressController extends BaseController
{
    @Autowired
    private IPostSaleUserAddressService postSaleUserAddressService;

    /**
     * 查询用户地址列表
     */
    @GetMapping("/list")
    @ApiOperation("查询用户地址列表")
    public TableDataInfo<PostSaleUserAddress> list(PostSaleUserAddress postSaleUserAddress)
    {
        startPage();
        List<PostSaleUserAddress> list = postSaleUserAddressService.selectPostSaleUserAddressList(postSaleUserAddress);
        return getDataTable(list);
    }

    /**
     * 获取用户地址详细信息
     */
    @ApiOperation("获取用户地址详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(postSaleUserAddressService.selectPostSaleUserAddressById(id));
    }

    /**
     * 新增用户地址
     */
    @ApiOperation("新增用户地址")
    @Log(title = "用户地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleUserAddress postSaleUserAddress)
    {
        return toAjax(postSaleUserAddressService.insertPostSaleUserAddress(postSaleUserAddress));
    }

    /**
     * 修改用户地址
     */
    @ApiOperation("修改用户地址")
    @Log(title = "用户地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleUserAddress postSaleUserAddress)
    {
        return toAjax(postSaleUserAddressService.updatePostSaleUserAddress(postSaleUserAddress));
    }

    /**
     * 删除用户地址
     */
    @ApiOperation("删除用户地址")
    @Log(title = "用户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(postSaleUserAddressService.deletePostSaleUserAddressByIds(ids));
    }
}
