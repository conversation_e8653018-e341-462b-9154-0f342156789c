package com.redbook.web.controller.tool;

import com.redbook.common.constant.CacheConstants;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.system.domain.Agent;
import com.redbook.system.service.IAgentAccountService;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IExclusiveShopService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController("/tool/verify")
public class VerifyController extends BaseController {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    private IExclusiveShopService exclusiveShopService;
    @GetMapping("/smsCode")
    @ApiOperation("短信验证码验证")
    public boolean verifyCode(String phone,String code) {
        String verifyKey = CacheConstants.CAPTCHA_SMS_CODE_KEY + phone;
        String captcha = redisCache.getCacheObject(verifyKey);
        if (captcha == null||!captcha.equals(code)) {
            return false;
        }
        redisCache.deleteObject(verifyKey);
        return true;
    }

    @GetMapping("/payPassword")
    @ApiOperation("支付密码验证")
    public boolean verifyPayPassword(String payPassword) {
        if (getLoginUser().getUser().getUserType().equals("00")) {
            return true;
        }
        //店长，验证专卖店支付密码
        if(getLoginUser().getUser().getRole().getRoleId().intValue()==106){
            List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().managerId(getLoginUser().getUserId()).build());
            if(exclusiveShopList.isEmpty()){
                return false;
            }
            return exclusiveShopService.checkPayPassword(exclusiveShopList.get(0).getId(),payPassword);
        }else if(getLoginUser().getUser().getRole().getRoleId().intValue()==100){//代理商，验证区域账户密码
            List<Agent> agentList = agentService.getByUserId(getLoginUser().getUserId());
            if (agentList.isEmpty()) {
                return false;
            }
            Agent agent = agentList.get(0);
            return agentAccountService.checkPayPassword(agent.getId(),payPassword);
        }
        return false;
    }
}
