package com.redbook.web.controller.xingye;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.util.XingYePayUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@RestController
@Api(tags = "兴业银行相关")
public class XingYeController extends BaseController {


    @Autowired
    XingYePayUtil xingYePayUtil;

    @RequestMapping("/pay")
    @ApiOperation("根据充值订单号获取支付二维码")
    public AjaxResult buildPayUrl(String orderId) {
        return AjaxResult.success(xingYePayUtil.createOrder(orderId));
    }

    @RequestMapping("/pay/notify")
    @Log(title = "兴业银行支付回调", businessType = BusinessType.UPDATE)
    public void payNotify(HttpServletRequest request) {
        xingYePayUtil.callback(parseRequst(request));
    }

    public static String parseRequst(HttpServletRequest request) {
        String body = "";
        try {
            ServletInputStream inputStream = request.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
            while (true) {
                String info = br.readLine();
                if (info == null) {
                    break;
                }
                if (body.isEmpty()) {
                    body = info;
                } else {
                    body += info;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return body;
    }
}
