package com.redbook.web.controller;

import com.redbook.common.core.controller.BaseController;
import com.redbook.system.service.postSale.IPostSaleRepairOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/postSale/expressOrderStatus/callback/")
@Api(tags = "售后-快递状态回调")
public class PostSaleExpressOrderCallBackController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(PostSaleExpressOrderCallBackController.class);
    @Autowired
    private IPostSaleRepairOrderService postSaleRepairOrderService;


    @PostMapping("sf")
    @ApiOperation("顺丰快递状态回调")
    public void sf(String content) {
        log.info("顺丰路由推送响应参数 sf/callback  content:{}", content);
        postSaleRepairOrderService.handleSfExpressOrderCallback(content);
    }

}
