package com.redbook.web.controller.pay;

import com.redbook.common.core.domain.AjaxResult;
import com.redbook.postsaleapi.model.WechatRefundOrderBean;
import com.redbook.postsaleapi.service.WechatPayService;
import com.redbook.service.PayService;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.model.Refund;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2024-12-12 13:48
 */
@RestController
@RequestMapping("/pay")
@Api(tags = "小程序-支付")
public class PayController {
    @Autowired
    PayService payService;
    @Autowired
    WechatPayService wechatPayService;

    /**
     * 查询支付微信订单
     * @param orderNo
     */
    @GetMapping("/getOrder")
    public AjaxResult<Transaction> getOrder(@RequestParam("orderNo") String orderNo) {
        Transaction order = payService.getOrder(orderNo);
        return AjaxResult.success(order);
    }
    /**
     * 查询退款微信订单
     * @param refundOrderNo
     */
    @GetMapping("/getRefundOrder")
    public AjaxResult<Refund> getRefundOrder(@RequestParam("refundOrderNo") String refundOrderNo) {
        Refund order = payService.getRefundOrder(refundOrderNo);
        return AjaxResult.success(order);
    }


    /**
     * 支付回调
     * @param request
     * @param response
     * @throws Exception
     */
    @PostMapping("/notify")
    public void payNotify(HttpServletRequest request, HttpServletResponse response){
        payService.payCallback(request, response);
    }

    /**
     * 退款回调
     * @param request
     * @param response
     * @throws Exception
     */
    @PostMapping("/refundNotify")
    public void refundNotify(HttpServletRequest request, HttpServletResponse response){
        payService.refundsCallback(request, response);
    }

    /**
     * 退款
     * @throws Exception
     */
    @PostMapping("/refunds")
    public void refunds(@RequestBody WechatRefundOrderBean refundOrderBean){
        payService.refunds(refundOrderBean);
    }

}
