package com.redbook.util.wehChat;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.Security;

/**
 * @Author: qwy
 * @Date: 2019/3/28 16:29
 * @Version 1.0
 */
@Component
public class AesCbcUtil {


    private final Environment env;
    private static Logger logger = LogManager.getLogger(AesCbcUtil.class);

    static {
        //BouncyCastle是一个开源的加解密解决方案，主页在http://www.bouncycastle.org/
        Security.addProvider(new BouncyCastleProvider());
    }

    @Autowired
    public AesCbcUtil(Environment env) {
        this.env = env;
    }

    /**
     * AES解密
     *
     * @param data           //密文，被加密的数据
     * @param key            //秘钥
     * @param iv             //偏移量
     * @param encodingFormat //解密后的结果需要进行的编码
     * @return
     * @throws Exception
     */
    public static String decrypt(String data, String key, String iv, String encodingFormat) throws Exception {
        //被加密的数据
        byte[] dataByte = Base64.decodeBase64(data.getBytes());
        //加密秘钥
        byte[] keyByte = Base64.decodeBase64(key.getBytes());
        //偏移量
        byte[] ivByte = Base64.decodeBase64(iv.getBytes());

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");

            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");

            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));

            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化

            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                return new String(resultByte, encodingFormat);
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description AES加密 PKCS5Padding
     * @Date 2019/8/1 11:46
     * @Param [input, key]
     **/
    public static String encryptByPKCS5(String input, String key) {
        byte[] crypted = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, skey);
            crypted = cipher.doFinal(input.getBytes());
        } catch (Exception e) {
            System.out.println(e.toString());
        }
        return new String(Base64.encodeBase64(crypted));
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description AES解密 PKCS5Padding
     * @Date 2019/8/1 11:46
     * @Param [input, key]
     **/
    public static String decryptByPKCS5(String input, String key) {
        byte[] output = null;
        try {
            SecretKeySpec skey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skey);
            output = cipher.doFinal(Base64.decodeBase64(input));
        } catch (Exception e) {
            logger.info(e.toString());
            return input;
        }
        return new String(output);
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 为手机加密提供的AES加密/身份证号加密
     * @Date 2019/8/2 9:10
     * @Param [input]
     **/
    public String encryptForPhone(String text) {
        return AesCbcUtil.encryptByPKCS5(text, env.getProperty("phoneSecretKey"));
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 为手机加密提供的AES解密/身份证号加密
     * @Date 2019/8/2 9:11
     * @Param [text]
     **/
    public String decryptForPhone(String text) {
        return AesCbcUtil.decryptByPKCS5(text, env.getProperty("phoneSecretKey"));
    }

    /**
     * 为录音文件解密
     *
     * @param text
     * @return
     */
    public String decryptForRecord(String text) {
        return AesCbcUtil.decryptByPKCS5(text, env.getProperty("recordKey"));
    }
}
