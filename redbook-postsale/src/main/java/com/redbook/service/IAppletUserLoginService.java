package com.redbook.service;


import com.redbook.dto.login.AppletUserAuthorizeRequestDto;
import com.redbook.dto.login.AppletUserCheckAuthorizeRequestDto;
import com.redbook.dto.login.AppletUserPhoneRequestDto;
import com.redbook.dto.login.AppletUserRequestDto;
import com.redbook.vo.login.AppletUserAuthorizeResponseVO;
import com.redbook.vo.login.AppletUserResponseVO;
import org.springframework.security.core.userdetails.UserDetails;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by Admin on 2019/3/26.
 */
public interface IAppletUserLoginService {

    AppletUserResponseVO checkAuthorize(AppletUserRequestDto appletUserRequestDto);

    AppletUserAuthorizeResponseVO addAuthorize(AppletUserAuthorizeRequestDto appletUserAuthorizeRequestVo);


    Boolean getPhoneNumber(AppletUserPhoneRequestDto appletUserPhoneRequestDto);

    AppletUserResponseVO refreshToken(String openId);
    UserDetails getUserTokenInfo(String token);

}
